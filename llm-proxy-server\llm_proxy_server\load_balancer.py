"""Load balancing strategies for LLM Proxy Server"""

import asyncio
import random
import time
from typing import Dict, List, Optional
from collections import defaultdict
import structlog

logger = structlog.get_logger()


class LoadBalancer:
    """Load balancer with multiple strategies"""
    
    def __init__(self, strategy: str = "least_connections"):
        self.strategy = strategy
        self.host_connections: Dict[str, int] = defaultdict(int)
        self.host_weights: Dict[str, float] = defaultdict(lambda: 1.0)
        self.host_health: Dict[str, bool] = defaultdict(lambda: True)
        self.last_selected: Dict[str, str] = {}  # model -> last selected host
        self.round_robin_index: Dict[str, int] = defaultdict(int)

        # Response time tracking for fastest server selection
        self.host_response_times: Dict[str, List[float]] = defaultdict(list)
        self.max_response_history = 10  # Keep last 10 response times per host

        logger.info("Load balancer initialized", strategy=strategy)
    
    async def select_host(self, model: str, available_hosts: List[str]) -> str:
        """Select the best host for a model based on the configured strategy"""
        if not available_hosts:
            raise ValueError("No available hosts for model")
        
        # Filter out unhealthy hosts
        healthy_hosts = [host for host in available_hosts if self.host_health.get(host, True)]
        
        if not healthy_hosts:
            logger.warning("No healthy hosts available, using all hosts", model=model)
            healthy_hosts = available_hosts
        
        if len(healthy_hosts) == 1:
            return healthy_hosts[0]
        
        if self.strategy == "round_robin":
            return self._round_robin_select(model, healthy_hosts)
        elif self.strategy == "least_connections":
            return self._least_connections_select(healthy_hosts)
        elif self.strategy == "weighted_random":
            return self._weighted_random_select(healthy_hosts)
        elif self.strategy == "random":
            return self._random_select(healthy_hosts)
        elif self.strategy == "fastest":
            return self._fastest_select(healthy_hosts)
        elif self.strategy == "adaptive":
            return self._adaptive_select(healthy_hosts)
        else:
            logger.warning("Unknown strategy, falling back to round_robin", strategy=self.strategy)
            return self._round_robin_select(model, healthy_hosts)
    
    def _round_robin_select(self, model: str, hosts: List[str]) -> str:
        """Round-robin selection"""
        index = self.round_robin_index[model] % len(hosts)
        self.round_robin_index[model] = (index + 1) % len(hosts)
        selected = hosts[index]
        
        logger.debug("Round-robin selection", model=model, selected=selected, index=index)
        return selected
    
    def _least_connections_select(self, hosts: List[str]) -> str:
        """Select host with least active connections"""
        min_connections = min(self.host_connections.get(host, 0) for host in hosts)
        candidates = [host for host in hosts if self.host_connections.get(host, 0) == min_connections]
        
        # If multiple hosts have the same connection count, pick randomly
        selected = random.choice(candidates)
        
        logger.debug("Least connections selection", 
                    selected=selected, 
                    connections=self.host_connections.get(selected, 0),
                    candidates=len(candidates))
        return selected
    
    def _weighted_random_select(self, hosts: List[str]) -> str:
        """Weighted random selection based on host weights"""
        weights = [self.host_weights.get(host, 1.0) for host in hosts]
        total_weight = sum(weights)
        
        if total_weight == 0:
            return random.choice(hosts)
        
        # Normalize weights
        normalized_weights = [w / total_weight for w in weights]
        
        # Select based on weights
        r = random.random()
        cumulative = 0.0
        for i, weight in enumerate(normalized_weights):
            cumulative += weight
            if r <= cumulative:
                selected = hosts[i]
                logger.debug("Weighted random selection", 
                           selected=selected, 
                           weight=self.host_weights.get(selected, 1.0))
                return selected
        
        # Fallback
        return hosts[-1]
    
    def _random_select(self, hosts: List[str]) -> str:
        """Pure random selection"""
        selected = random.choice(hosts)
        logger.debug("Random selection", selected=selected)
        return selected

    def _fastest_select(self, hosts: List[str]) -> str:
        """Select host with fastest average response time"""
        host_avg_times = {}

        for host in hosts:
            response_times = self.host_response_times.get(host, [])
            if response_times:
                # Calculate average of recent response times
                avg_time = sum(response_times) / len(response_times)
                host_avg_times[host] = avg_time
            else:
                # No history, assign neutral time (will be selected if all hosts have no history)
                host_avg_times[host] = float('inf')

        # Select host with minimum average response time
        if host_avg_times:
            selected = min(host_avg_times.keys(), key=lambda h: host_avg_times[h])
            avg_time = host_avg_times[selected]

            logger.debug("Fastest selection",
                        selected=selected,
                        avg_response_time=avg_time,
                        sample_size=len(self.host_response_times.get(selected, [])))
            return selected

        # Fallback to random if no data
        return random.choice(hosts)

    def _adaptive_select(self, hosts: List[str]) -> str:
        """Adaptive selection combining response time and connection count"""
        host_scores = {}

        for host in hosts:
            # Get average response time (lower is better)
            response_times = self.host_response_times.get(host, [])
            avg_response_time = sum(response_times) / len(response_times) if response_times else 1.0

            # Get current connections (lower is better)
            connections = self.host_connections.get(host, 0)

            # Calculate composite score (lower is better)
            # Weight: 70% response time, 30% connection count
            response_weight = 0.7
            connection_weight = 0.3

            # Normalize connection count (assume max 10 connections for normalization)
            normalized_connections = min(connections / 10.0, 1.0)

            # Composite score (lower is better)
            score = (response_weight * avg_response_time) + (connection_weight * normalized_connections)
            host_scores[host] = score

        if host_scores:
            selected = min(host_scores.keys(), key=lambda h: host_scores[h])
            score = host_scores[selected]

            logger.debug("Adaptive selection",
                        selected=selected,
                        score=score,
                        avg_response_time=sum(self.host_response_times.get(selected, [1.0])) / len(self.host_response_times.get(selected, [1.0])),
                        connections=self.host_connections.get(selected, 0))
            return selected

        # Fallback to random
        return random.choice(hosts)
    
    def increment_connections(self, host: str):
        """Increment connection count for a host"""
        self.host_connections[host] += 1
        logger.debug("Connection incremented", host=host, connections=self.host_connections[host])
    
    def decrement_connections(self, host: str):
        """Decrement connection count for a host"""
        if self.host_connections[host] > 0:
            self.host_connections[host] -= 1
        logger.debug("Connection decremented", host=host, connections=self.host_connections[host])
    
    def set_host_weight(self, host: str, weight: float):
        """Set weight for a host (used in weighted strategies)"""
        self.host_weights[host] = weight
        logger.info("Host weight updated", host=host, weight=weight)
    
    def set_host_health(self, host: str, healthy: bool):
        """Set health status for a host"""
        old_status = self.host_health.get(host, True)
        self.host_health[host] = healthy

        if old_status != healthy:
            logger.info("Host health changed", host=host, healthy=healthy)

    def record_response_time(self, host: str, response_time: float):
        """Record response time for a host (used in fastest/adaptive strategies)"""
        if host not in self.host_response_times:
            self.host_response_times[host] = []

        # Add new response time
        self.host_response_times[host].append(response_time)

        # Keep only recent history
        if len(self.host_response_times[host]) > self.max_response_history:
            self.host_response_times[host] = self.host_response_times[host][-self.max_response_history:]

        logger.debug("Response time recorded",
                    host=host,
                    response_time=response_time,
                    avg_time=sum(self.host_response_times[host]) / len(self.host_response_times[host]))

    def get_host_response_stats(self) -> Dict[str, Dict]:
        """Get response time statistics for all hosts"""
        stats = {}
        for host, times in self.host_response_times.items():
            if times:
                stats[host] = {
                    'avg_response_time': sum(times) / len(times),
                    'min_response_time': min(times),
                    'max_response_time': max(times),
                    'sample_count': len(times),
                    'recent_times': times[-5:]  # Last 5 response times
                }
            else:
                stats[host] = {
                    'avg_response_time': 0.0,
                    'min_response_time': 0.0,
                    'max_response_time': 0.0,
                    'sample_count': 0,
                    'recent_times': []
                }
        return stats
    
    def get_host_stats(self) -> Dict[str, Dict]:
        """Get statistics for all hosts"""
        all_hosts = set(list(self.host_connections.keys()) +
                       list(self.host_weights.keys()) +
                       list(self.host_health.keys()) +
                       list(self.host_response_times.keys()))

        stats = {}
        for host in all_hosts:
            response_times = self.host_response_times.get(host, [])
            stats[host] = {
                'connections': self.host_connections.get(host, 0),
                'weight': self.host_weights.get(host, 1.0),
                'healthy': self.host_health.get(host, True),
                'avg_response_time': sum(response_times) / len(response_times) if response_times else 0.0,
                'response_samples': len(response_times)
            }

        return stats
    
    def reset_stats(self):
        """Reset all statistics"""
        self.host_connections.clear()
        self.round_robin_index.clear()
        self.host_response_times.clear()
        logger.info("Load balancer stats reset")
