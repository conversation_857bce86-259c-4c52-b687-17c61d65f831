# LLM Proxy Server + Open WebUI Integration Guide

## 🎯 Overview

This integration combines your LLM Proxy Server with Open WebUI to create a complete, user-friendly LLM interface that load balances across your Ollama servers.

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Open WebUI    │───▶│ LLM Proxy Server│───▶│  Ollama Servers │
│  (Port 3000)    │    │  (Port 11440)   │    │ (Your 3 servers)│
│                 │    │                 │    │                 │
│ • Web Interface │    │ • Load Balancer │    │ • lynn-pc       │
│ • Chat UI       │    │ • Health Checks │    │ • home-game-srv │
│ • User Management│    │ • Metrics       │    │ • t5810         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 Quick Start

### 1. Start the Complete Stack
```powershell
cd C:\home-repos\llm_proxy_server\llm-proxy-server
.\start-local-stack.ps1
```

### 2. Access Open WebUI
1. Open http://localhost:3000 in your browser
2. Create your first user account (becomes admin)
3. Start chatting with your LLMs!

### 3. Test Everything
```powershell
.\test-local-setup.ps1
```

## 🔧 Configuration Details

### Open WebUI Settings
- **URL**: http://localhost:3000
- **Backend**: Points to LLM Proxy Server at `http://llm-proxy-server:11440`
- **Authentication**: Enabled (create your own account)
- **Data**: Persisted in Docker volume `open-webui-data`

### LLM Proxy Server Settings
- **URL**: http://localhost:11440
- **Load Balancing**: Least connections strategy
- **Health Checks**: Every 30 seconds
- **Metrics**: Available at `/proxy/metrics`
- **Debug Mode**: Enabled for local testing

### Your Ollama Servers
- **lynn-pc**: ************:11434
- **home-game-server**: ************:11434
- **t5810**: ************:11434

## 🎮 Using the Interface

### Chat Features
- **Model Selection**: Choose from models available across all your servers
- **Streaming Responses**: Real-time response streaming
- **Chat History**: Persistent conversation history
- **Load Balancing**: Automatic distribution across your servers

### Admin Features
- **User Management**: Create and manage users
- **Model Management**: View available models
- **System Settings**: Configure various options

## 📊 Monitoring

### Health Checks
- **Proxy Health**: http://localhost:11440/proxy/health
- **Proxy Metrics**: http://localhost:11440/proxy/metrics
- **WebUI Health**: http://localhost:3000/health

### Logs
```powershell
# View all logs
docker-compose -f docker-compose.local.yml logs -f

# View specific service logs
docker-compose -f docker-compose.local.yml logs -f llm-proxy-server
docker-compose -f docker-compose.local.yml logs -f open-webui
```

## 🛠️ Management Commands

### Start/Stop
```powershell
# Start everything
.\start-local-stack.ps1

# Stop everything
.\stop-local-stack.ps1

# Restart specific service
docker-compose -f docker-compose.local.yml restart llm-proxy-server
docker-compose -f docker-compose.local.yml restart open-webui
```

### Troubleshooting
```powershell
# Check container status
docker-compose -f docker-compose.local.yml ps

# View logs
docker-compose -f docker-compose.local.yml logs

# Rebuild and restart
docker-compose -f docker-compose.local.yml up --build -d
```

## 🔒 Security Notes

### Local Testing
- Authentication is optional for local testing
- All traffic stays within your local network
- No external connections required

### Production Considerations
- Enable authentication in both services
- Use HTTPS/TLS certificates
- Configure proper firewall rules
- Set strong passwords and secrets

## 🎯 Benefits

### For Users
- **Easy Interface**: No command-line needed
- **Model Choice**: Access all your models in one place
- **Chat History**: Keep track of conversations
- **Real-time**: Streaming responses

### For Administrators
- **Load Balancing**: Automatic distribution across servers
- **Health Monitoring**: Know when servers are down
- **Metrics**: Track usage and performance
- **Centralized**: One interface for all models

## 🚀 Next Steps

1. **Test the Setup**: Use the provided test scripts
2. **Explore Models**: Try different models from your servers
3. **Monitor Performance**: Check metrics and health endpoints
4. **Scale Up**: Add more Ollama servers to the proxy configuration
5. **Deploy to Production**: Use the production docker-compose when ready

## 📞 Support

If you encounter issues:
1. Check the logs: `docker-compose -f docker-compose.local.yml logs`
2. Verify your Ollama servers are running
3. Test individual components with the test script
4. Check network connectivity between containers

Happy chatting with your LLMs! 🤖✨
