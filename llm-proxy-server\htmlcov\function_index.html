<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">81%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.0">coverage.py v7.10.0</a>,
            created at 2025-07-24 13:32 -0400
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1___init___py.html">llm_proxy_server\__init__.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html#t14">llm_proxy_server\auth.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html#t14"><data value='init__'>AuthManager.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html#t19">llm_proxy_server\auth.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html#t19"><data value='load_users'>AuthManager._load_users</data></a></td>
                <td>23</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="21 23">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html#t57">llm_proxy_server\auth.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html#t57"><data value='authenticate'>AuthManager.authenticate</data></a></td>
                <td>17</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="17 17">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html#t88">llm_proxy_server\auth.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html#t88"><data value='reload_users'>AuthManager.reload_users</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html#t93">llm_proxy_server\auth.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html#t93"><data value='get_user_count'>AuthManager.get_user_count</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html#t97">llm_proxy_server\auth.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html#t97"><data value='is_user_admin'>AuthManager.is_user_admin</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html">llm_proxy_server\auth.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_py.html#t38">llm_proxy_server\config.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_py.html#t38"><data value='init__'>Settings.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_py.html#t62">llm_proxy_server\config.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_py.html#t62"><data value='get_settings'>get_settings</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_py.html">llm_proxy_server\config.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>21</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="21 21">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t16">llm_proxy_server\load_balancer.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t16"><data value='init__'>LoadBalancer.__init__</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t26">llm_proxy_server\load_balancer.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t26"><data value='select_host'>LoadBalancer.select_host</data></a></td>
                <td>18</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="16 18">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t53">llm_proxy_server\load_balancer.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t53"><data value='round_robin_select'>LoadBalancer._round_robin_select</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t62">llm_proxy_server\load_balancer.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t62"><data value='least_connections_select'>LoadBalancer._least_connections_select</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t76">llm_proxy_server\load_balancer.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t76"><data value='weighted_random_select'>LoadBalancer._weighted_random_select</data></a></td>
                <td>14</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="12 14">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t102">llm_proxy_server\load_balancer.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t102"><data value='random_select'>LoadBalancer._random_select</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t108">llm_proxy_server\load_balancer.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t108"><data value='increment_connections'>LoadBalancer.increment_connections</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t113">llm_proxy_server\load_balancer.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t113"><data value='decrement_connections'>LoadBalancer.decrement_connections</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t119">llm_proxy_server\load_balancer.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t119"><data value='set_host_weight'>LoadBalancer.set_host_weight</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t124">llm_proxy_server\load_balancer.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t124"><data value='set_host_health'>LoadBalancer.set_host_health</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t132">llm_proxy_server\load_balancer.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t132"><data value='get_host_stats'>LoadBalancer.get_host_stats</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t145">llm_proxy_server\load_balancer.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t145"><data value='reset_stats'>LoadBalancer.reset_stats</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html">llm_proxy_server\load_balancer.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t55">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t55"><data value='get_current_user'>get_current_user</data></a></td>
                <td>9</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="8 9">89%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t83">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t83"><data value='lifespan'>lifespan</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t101">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t101"><data value='health_check_loop'>health_check_loop</data></a></td>
                <td>7</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="5 7">71%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t133">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t133"><data value='logging_middleware'>logging_middleware</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t155">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t155"><data value='chat'>chat</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t165">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t165"><data value='stream_chat'>chat.stream_chat</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t186">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t186"><data value='generate'>generate</data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t196">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t196"><data value='stream_generate'>generate.stream_generate</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t217">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t217"><data value='embed'>embed</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t231">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t231"><data value='show'>show</data></a></td>
                <td>6</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="0 6">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t245">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t245"><data value='list_models'>list_models</data></a></td>
                <td>6</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="3 6">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t256">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t256"><data value='version'>version</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t263">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t263"><data value='get_status'>get_status</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t272">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t272"><data value='get_metrics'>get_metrics</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t281">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t281"><data value='health_check'>health_check</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t294">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html#t294"><data value='root'>root</data></a></td>
                <td>1</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="0 1">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>56</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="54 56">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html">llm_proxy_server\models.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>91</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="91 91">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t15">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t15"><data value='init__'>MetricsManager.__init__</data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t44">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t44"><data value='record_request'>MetricsManager.record_request</data></a></td>
                <td>20</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="18 20">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t91">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t91"><data value='record_health_check'>MetricsManager.record_health_check</data></a></td>
                <td>4</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="3 4">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t105">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t105"><data value='increment_active_connections'>MetricsManager.increment_active_connections</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t109">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t109"><data value='decrement_active_connections'>MetricsManager.decrement_active_connections</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t114">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t114"><data value='get_request_stats'>MetricsManager.get_request_stats</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t135">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t135"><data value='get_host_stats'>MetricsManager.get_host_stats</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t161">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t161"><data value='get_model_stats'>MetricsManager.get_model_stats</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t177">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t177"><data value='get_health_status'>MetricsManager.get_health_status</data></a></td>
                <td>10</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="9 10">90%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t201">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t201"><data value='get_recent_activity'>MetricsManager.get_recent_activity</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t205">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t205"><data value='generate_metrics'>MetricsManager.generate_metrics</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t215">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t215"><data value='reset_metrics'>MetricsManager.reset_metrics</data></a></td>
                <td>13</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="13 13">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t30">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t30"><data value='init__'>ProxyManager.__init__</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t38">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t38"><data value='initialize'>ProxyManager.initialize</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t44">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t44"><data value='shutdown'>ProxyManager.shutdown</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t50">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t50"><data value='load_host_configs'>ProxyManager._load_host_configs</data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t67">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t67"><data value='initialize_clients'>ProxyManager._initialize_clients</data></a></td>
                <td>9</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="6 9">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t79">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t79"><data value='discover_models'>ProxyManager._discover_models</data></a></td>
                <td>14</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="11 14">79%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t99">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t99"><data value='handle_chat_stream'>ProxyManager.handle_chat_stream</data></a></td>
                <td>33</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="16 33">48%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t171">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t171"><data value='handle_chat'>ProxyManager.handle_chat</data></a></td>
                <td>32</td>
                <td>17</td>
                <td>0</td>
                <td class="right" data-ratio="15 32">47%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t243">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t243"><data value='handle_generate_stream'>ProxyManager.handle_generate_stream</data></a></td>
                <td>37</td>
                <td>37</td>
                <td>0</td>
                <td class="right" data-ratio="0 37">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t324">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t324"><data value='handle_generate'>ProxyManager.handle_generate</data></a></td>
                <td>36</td>
                <td>18</td>
                <td>0</td>
                <td class="right" data-ratio="18 36">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t405">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t405"><data value='handle_embed'>ProxyManager.handle_embed</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t428">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t428"><data value='handle_show'>ProxyManager.handle_show</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t448">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t448"><data value='get_client_for_model'>ProxyManager._get_client_for_model</data></a></td>
                <td>7</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="6 7">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t461">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t461"><data value='get_host_for_client'>ProxyManager._get_host_for_client</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t468">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t468"><data value='list_models'>ProxyManager.list_models</data></a></td>
                <td>12</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="11 12">92%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t492">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t492"><data value='get_status'>ProxyManager.get_status</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>27</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="27 27">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>771</td>
                <td>149</td>
                <td>0</td>
                <td class="right" data-ratio="622 771">81%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.0">coverage.py v7.10.0</a>,
            created at 2025-07-24 13:32 -0400
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
