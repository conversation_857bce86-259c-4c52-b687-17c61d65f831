{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.10.0", "globals": "ff8ae9b5896ba02668ddaef35dde331d", "files": {"z_bdae19fbfd4018c1___init___py": {"hash": "a81c5dabedf7d78f04974fd1a3ee0cac", "index": {"url": "z_bdae19fbfd4018c1___init___py.html", "file": "llm_proxy_server\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bdae19fbfd4018c1_auth_py": {"hash": "fb90f6beca6b42426d8b5ba9079b1462", "index": {"url": "z_bdae19fbfd4018c1_auth_py.html", "file": "llm_proxy_server\\auth.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 61, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bdae19fbfd4018c1_config_py": {"hash": "0f4d94edc05af342d216bf21887eed44", "index": {"url": "z_bdae19fbfd4018c1_config_py.html", "file": "llm_proxy_server\\config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 25, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bdae19fbfd4018c1_load_balancer_py": {"hash": "2b8c2c06dc401e3748c720fbdda43899", "index": {"url": "z_bdae19fbfd4018c1_load_balancer_py.html", "file": "llm_proxy_server\\load_balancer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 87, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bdae19fbfd4018c1_main_py": {"hash": "f56683ade34f35f72eb6f6daa1ed9342", "index": {"url": "z_bdae19fbfd4018c1_main_py.html", "file": "llm_proxy_server\\main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 140, "n_excluded": 0, "n_missing": 23, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bdae19fbfd4018c1_models_py": {"hash": "039ff453a84261044cc45972e795ab11", "index": {"url": "z_bdae19fbfd4018c1_models_py.html", "file": "llm_proxy_server\\models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 91, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bdae19fbfd4018c1_monitoring_py": {"hash": "2d025bcdd674fce398fef7a8ae31b263", "index": {"url": "z_bdae19fbfd4018c1_monitoring_py.html", "file": "llm_proxy_server\\monitoring.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 108, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_bdae19fbfd4018c1_proxy_manager_py": {"hash": "9c78484c081978f9663a602554ac88f9", "index": {"url": "z_bdae19fbfd4018c1_proxy_manager_py.html", "file": "llm_proxy_server\\proxy_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 257, "n_excluded": 0, "n_missing": 116, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}