{".class": "MypyFile", "_fullname": "llm_proxy_server.load_balancer", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "LoadBalancer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "llm_proxy_server.load_balancer.LoadBalancer", "name": "LoadBalancer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "llm_proxy_server.load_balancer.LoadBalancer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "llm_proxy_server.load_balancer", "mro": ["llm_proxy_server.load_balancer.LoadBalancer", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "strategy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "llm_proxy_server.load_balancer.LoadBalancer.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "strategy"], "arg_types": ["llm_proxy_server.load_balancer.LoadBalancer", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of LoadBalancer", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_adaptive_select": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hosts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "llm_proxy_server.load_balancer.LoadBalancer._adaptive_select", "name": "_adaptive_select", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hosts"], "arg_types": ["llm_proxy_server.load_balancer.LoadBalancer", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_adaptive_select of LoadBalancer", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fastest_select": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hosts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "llm_proxy_server.load_balancer.LoadBalancer._fastest_select", "name": "_fastest_select", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hosts"], "arg_types": ["llm_proxy_server.load_balancer.LoadBalancer", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_fastest_select of LoadBalancer", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_least_connections_select": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hosts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "llm_proxy_server.load_balancer.LoadBalancer._least_connections_select", "name": "_least_connections_select", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hosts"], "arg_types": ["llm_proxy_server.load_balancer.LoadBalancer", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_least_connections_select of LoadBalancer", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_random_select": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hosts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "llm_proxy_server.load_balancer.LoadBalancer._random_select", "name": "_random_select", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hosts"], "arg_types": ["llm_proxy_server.load_balancer.LoadBalancer", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_random_select of LoadBalancer", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_round_robin_select": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "model", "hosts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "llm_proxy_server.load_balancer.LoadBalancer._round_robin_select", "name": "_round_robin_select", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "model", "hosts"], "arg_types": ["llm_proxy_server.load_balancer.LoadBalancer", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_round_robin_select of LoadBalancer", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_weighted_random_select": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "hosts"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "llm_proxy_server.load_balancer.LoadBalancer._weighted_random_select", "name": "_weighted_random_select", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "hosts"], "arg_types": ["llm_proxy_server.load_balancer.LoadBalancer", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_weighted_random_select of LoadBalancer", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "decrement_connections": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "host"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "llm_proxy_server.load_balancer.LoadBalancer.decrement_connections", "name": "decrement_connections", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "host"], "arg_types": ["llm_proxy_server.load_balancer.LoadBalancer", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "decrement_connections of LoadBalancer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_host_response_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "llm_proxy_server.load_balancer.LoadBalancer.get_host_response_stats", "name": "get_host_response_stats", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["llm_proxy_server.load_balancer.LoadBalancer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_host_response_stats of LoadBalancer", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_host_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "llm_proxy_server.load_balancer.LoadBalancer.get_host_stats", "name": "get_host_stats", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["llm_proxy_server.load_balancer.LoadBalancer"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_host_stats of LoadBalancer", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "host_connections": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "llm_proxy_server.load_balancer.LoadBalancer.host_connections", "name": "host_connections", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "host_health": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "llm_proxy_server.load_balancer.LoadBalancer.host_health", "name": "host_health", "type": {".class": "Instance", "args": ["builtins.str", "builtins.bool"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "host_response_times": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "llm_proxy_server.load_balancer.LoadBalancer.host_response_times", "name": "host_response_times", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": ["builtins.float"], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "host_weights": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "llm_proxy_server.load_balancer.LoadBalancer.host_weights", "name": "host_weights", "type": {".class": "Instance", "args": ["builtins.str", "builtins.float"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "increment_connections": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "host"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "llm_proxy_server.load_balancer.LoadBalancer.increment_connections", "name": "increment_connections", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "host"], "arg_types": ["llm_proxy_server.load_balancer.LoadBalancer", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "increment_connections of LoadBalancer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "last_selected": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "llm_proxy_server.load_balancer.LoadBalancer.last_selected", "name": "last_selected", "type": {".class": "Instance", "args": ["builtins.str", "builtins.str"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "max_response_history": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "llm_proxy_server.load_balancer.LoadBalancer.max_response_history", "name": "max_response_history", "type": "builtins.int"}}, "record_response_time": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "host", "response_time"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "llm_proxy_server.load_balancer.LoadBalancer.record_response_time", "name": "record_response_time", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "host", "response_time"], "arg_types": ["llm_proxy_server.load_balancer.LoadBalancer", "builtins.str", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "record_response_time of LoadBalancer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "reset_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "llm_proxy_server.load_balancer.LoadBalancer.reset_stats", "name": "reset_stats", "type": null}}, "round_robin_index": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "llm_proxy_server.load_balancer.LoadBalancer.round_robin_index", "name": "round_robin_index", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "select_host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "model", "available_hosts"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "llm_proxy_server.load_balancer.LoadBalancer.select_host", "name": "select_host", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "model", "available_hosts"], "arg_types": ["llm_proxy_server.load_balancer.LoadBalancer", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "select_host of LoadBalancer", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "builtins.str"], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_host_health": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "host", "healthy"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "llm_proxy_server.load_balancer.LoadBalancer.set_host_health", "name": "set_host_health", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "host", "healthy"], "arg_types": ["llm_proxy_server.load_balancer.LoadBalancer", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_host_health of LoadBalancer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "set_host_weight": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "host", "weight"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "llm_proxy_server.load_balancer.LoadBalancer.set_host_weight", "name": "set_host_weight", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "host", "weight"], "arg_types": ["llm_proxy_server.load_balancer.LoadBalancer", "builtins.str", "builtins.float"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_host_weight of LoadBalancer", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "strategy": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "llm_proxy_server.load_balancer.LoadBalancer.strategy", "name": "strategy", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "llm_proxy_server.load_balancer.LoadBalancer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "llm_proxy_server.load_balancer.LoadBalancer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "llm_proxy_server.load_balancer.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "llm_proxy_server.load_balancer.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "llm_proxy_server.load_balancer.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "llm_proxy_server.load_balancer.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "llm_proxy_server.load_balancer.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "llm_proxy_server.load_balancer.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "defaultdict": {".class": "SymbolTableNode", "cross_ref": "collections.defaultdict", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "llm_proxy_server.load_balancer.logger", "name": "logger", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "random": {".class": "SymbolTableNode", "cross_ref": "random", "kind": "Gdef"}, "structlog": {".class": "SymbolTableNode", "cross_ref": "structlog", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}}, "path": "C:\\home-repos\\llm_proxy_server\\llm-proxy-server\\llm_proxy_server\\load_balancer.py"}