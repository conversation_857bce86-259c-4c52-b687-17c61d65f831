{"data_mtime": 1753376689, "dep_lines": [40, 45, 5, 44, 47, 48, 49, 2, 4, 9, 10, 13, 14, 15, 16, 17, 18, 22, 25, 37, 39, 857, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 5, 10, 5, 10, 10, 5, 5, 5, 10, 10, 25, 5, 30, 30, 30, 30, 30], "dependencies": ["_pytest._code.source", "_pytest._io.saferepr", "collections.abc", "_pytest._io", "_pytest.compat", "_pytest.deprecated", "_pytest.pathlib", "__future__", "ast", "dataclasses", "inspect", "io", "os", "pathlib", "re", "sys", "traceback", "types", "typing", "pluggy", "_pytest", "typing_extensions", "builtins", "_frozen_importlib", "_pytest._io.terminalwriter", "_typeshed", "abc", "enum"], "hash": "8328215e3eae4bc1a9ba0f9c64f59b9ebae0f45a", "id": "_pytest._code.code", "ignore_all": true, "interface_hash": "2dbf70e168273d520f01df50237f2e1f90a3966f", "mtime": 1753376605, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\llm_proxy_server\\venv\\Lib\\site-packages\\_pytest\\_code\\code.py", "plugin_data": null, "size": 55913, "suppressed": [], "version_id": "1.15.0"}