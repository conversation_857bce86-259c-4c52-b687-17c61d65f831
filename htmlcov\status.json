{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.10.0", "globals": "ac220390e1ea47f698d33e9de8655c13", "files": {"z_5c7853417c741ddb___init___py": {"hash": "a81c5dabedf7d78f04974fd1a3ee0cac", "index": {"url": "z_5c7853417c741ddb___init___py.html", "file": "llm-proxy-server\\llm_proxy_server\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 2, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5c7853417c741ddb_auth_py": {"hash": "fb90f6beca6b42426d8b5ba9079b1462", "index": {"url": "z_5c7853417c741ddb_auth_py.html", "file": "llm-proxy-server\\llm_proxy_server\\auth.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 61, "n_excluded": 0, "n_missing": 2, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5c7853417c741ddb_config_py": {"hash": "0f4d94edc05af342d216bf21887eed44", "index": {"url": "z_5c7853417c741ddb_config_py.html", "file": "llm-proxy-server\\llm_proxy_server\\config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 25, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5c7853417c741ddb_load_balancer_py": {"hash": "67d5a362a87d9ed728fa59f98ff516f4", "index": {"url": "z_5c7853417c741ddb_load_balancer_py.html", "file": "llm-proxy-server\\llm_proxy_server\\load_balancer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 144, "n_excluded": 0, "n_missing": 44, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5c7853417c741ddb_main_py": {"hash": "040fc273c651f5557fa5e5cdba648041", "index": {"url": "z_5c7853417c741ddb_main_py.html", "file": "llm-proxy-server\\llm_proxy_server\\main.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 165, "n_excluded": 0, "n_missing": 38, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5c7853417c741ddb_models_py": {"hash": "f5e675f5c9dbe80680ccd891e8baedcf", "index": {"url": "z_5c7853417c741ddb_models_py.html", "file": "llm-proxy-server\\llm_proxy_server\\models.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 116, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5c7853417c741ddb_monitoring_py": {"hash": "2d025bcdd674fce398fef7a8ae31b263", "index": {"url": "z_5c7853417c741ddb_monitoring_py.html", "file": "llm-proxy-server\\llm_proxy_server\\monitoring.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 108, "n_excluded": 0, "n_missing": 4, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_5c7853417c741ddb_proxy_manager_py": {"hash": "6700b625351e810411d1b8060cb549b8", "index": {"url": "z_5c7853417c741ddb_proxy_manager_py.html", "file": "llm-proxy-server\\llm_proxy_server\\proxy_manager.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 352, "n_excluded": 0, "n_missing": 198, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}