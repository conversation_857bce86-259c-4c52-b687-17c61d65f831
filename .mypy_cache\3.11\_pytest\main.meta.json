{"data_mtime": 1753376691, "dep_lines": [35, 36, 6, 15, 27, 28, 29, 37, 38, 43, 45, 47, 53, 3, 5, 11, 12, 13, 14, 16, 17, 18, 19, 23, 25, 27, 51, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 10, 5, 5, 5, 5, 5, 5, 25, 5, 10, 10, 10, 10, 10, 10, 5, 10, 5, 10, 10, 20, 25, 5, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest.config.argparsing", "_pytest.config.compat", "collections.abc", "importlib.util", "_pytest.nodes", "_pytest._code", "_pytest.config", "_pytest.outcomes", "_pytest.pathlib", "_pytest.reports", "_pytest.runner", "_pytest.warning_types", "_pytest.fixtures", "__future__", "<PERSON><PERSON><PERSON><PERSON>", "dataclasses", "fnmatch", "functools", "importlib", "os", "pathlib", "sys", "typing", "warnings", "pluggy", "_pytest", "typing_extensions", "builtins", "_frozen_importlib", "_pytest.config.exceptions", "abc", "enum", "pluggy._hooks", "pluggy._manager", "pluggy._tracing", "types"], "hash": "9c383822c59b6d0f36a2a6d0bfdf1a1abd5ad126", "id": "_pytest.main", "ignore_all": true, "interface_hash": "f24095f979d478fa90f0f1dfedda419f8e00db33", "mtime": 1753376605, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\llm_proxy_server\\venv\\Lib\\site-packages\\_pytest\\main.py", "plugin_data": null, "size": 37689, "suppressed": [], "version_id": "1.15.0"}