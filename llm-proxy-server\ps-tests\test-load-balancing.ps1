# PowerShell script to test load balancing strategies

Write-Host "⚖️  Load Balancing Strategy Test" -ForegroundColor Green
Write-Host "===============================" -ForegroundColor Green

# Load System.Web for URL encoding
Add-Type -AssemblyName System.Web

# Function to map IP addresses to server names
function Get-ServerFromHost {
    param([string]$HostIP)
    
    if ($HostIP -match "http://([0-9.]+):") {
        $ip = $matches[1]
    } else {
        $ip = $HostIP
    }
    
    switch ($ip) {
        "************" { return "lynn-pc" }
        "************" { return "home-game-server" }
        "************" { return "t5810" }
        default { return "unknown ($ip)" }
    }
}

# Function to update load balancing strategy
function Set-LoadBalancingStrategy {
    param([string]$Strategy)
    
    Write-Host "`n⚙️  Setting strategy to: $Strategy" -ForegroundColor Yellow
    
    $composeFile = "docker-compose.local.yml"
    $content = Get-Content $composeFile -Raw
    $newContent = $content -replace "LOAD_BALANCER_STRATEGY=\w+", "LOAD_BALANCER_STRATEGY=$Strategy"
    Set-Content $composeFile $newContent -NoNewline
    
    # Restart proxy
    $null = docker-compose -f docker-compose.local.yml up -d --no-deps llm-proxy-server 2>&1
    
    # Wait for ready
    $maxWait = 20
    $waited = 0
    while ($waited -lt $maxWait) {
        try {
            $null = Invoke-RestMethod -Uri "http://localhost:11440/proxy/health" -TimeoutSec 3
            Write-Host "✅ Proxy ready with $Strategy strategy" -ForegroundColor Green
            return $true
        }
        catch {
            Start-Sleep -Seconds 2
            $waited += 2
        }
    }
    return $false
}

# Function to test load balancing
function Test-LoadBalancing {
    param(
        [string]$Model,
        [string]$Strategy,
        [int]$RequestCount = 6
    )
    
    Write-Host "`n🧪 Testing $Strategy with $Model" -ForegroundColor Cyan
    
    $serverCounts = @{}
    $responseTimes = @()
    
    for ($i = 1; $i -le $RequestCount; $i++) {
        try {
            $body = @{
                model = $Model
                messages = @(@{
                    role = "user"
                    content = "Test $Strategy - Request #$i. Respond with 'OK $i'."
                })
                stream = $false
            } | ConvertTo-Json -Depth 3
            
            $startTime = Get-Date
            $response = Invoke-RestMethod -Uri "http://localhost:11440/api/chat" -Method POST -Body $body -ContentType "application/json" -TimeoutSec 20
            $duration = ($endTime = Get-Date; ($endTime - $startTime).TotalSeconds)
            
            if ($response -and $response.proxy_server) {
                $serverName = Get-ServerFromHost -HostIP $response.proxy_server
                $serverCounts[$serverName] = ($serverCounts[$serverName] ?? 0) + 1
                $responseTimes += $duration
                
                Write-Host "  ✅ Request $i`: $serverName - $([Math]::Round($duration, 2))s" -ForegroundColor Green
            }
        }
        catch {
            Write-Host "  ❌ Request $i failed: $($_.Exception.Message)" -ForegroundColor Red
        }
        Start-Sleep -Seconds 0.5
    }
    
    # Show results
    Write-Host "`n📊 Results:" -ForegroundColor White
    foreach ($server in $serverCounts.Keys | Sort-Object) {
        $count = $serverCounts[$server]
        $percentage = [Math]::Round(($count / $RequestCount) * 100, 1)
        Write-Host "  • $server`: $count requests ($percentage%)" -ForegroundColor Gray
    }
    
    if ($responseTimes.Count -gt 0) {
        $avgTime = ($responseTimes | Measure-Object -Average).Average
        Write-Host "Average response time: $([Math]::Round($avgTime, 2))s" -ForegroundColor Gray
    }
    
    return @{
        Strategy = $Strategy
        Model = $Model
        ServerDistribution = $serverCounts
        AvgResponseTime = if ($responseTimes.Count -gt 0) { ($responseTimes | Measure-Object -Average).Average } else { 0 }
    }
}

# Main execution
try {
    # Check proxy health
    Write-Host "`n🔍 Checking proxy..." -ForegroundColor Yellow
    try {
        $null = Invoke-RestMethod -Uri "http://localhost:11440/proxy/health" -TimeoutSec 5
        Write-Host "✅ Proxy is running" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Proxy not responding. Start with: docker-compose -f docker-compose.local.yml up -d" -ForegroundColor Red
        exit 1
    }
    
    # Get available models
    $models = Invoke-RestMethod -Uri "http://localhost:11440/api/tags" -TimeoutSec 10
    $testModel = "llama3:latest"
    
    if ($models.models.name -notcontains $testModel) {
        $testModel = $models.models[0].name
        Write-Host "⚠️  Using $testModel for testing" -ForegroundColor Yellow
    } else {
        Write-Host "✅ Using $testModel for testing" -ForegroundColor Green
    }
    
    # Test strategies
    $strategies = @("round_robin", "fastest", "adaptive")
    $results = @()
    
    foreach ($strategy in $strategies) {
        Write-Host "`n" + ("=" * 50) -ForegroundColor Gray
        Write-Host "🎯 TESTING: $($strategy.ToUpper())" -ForegroundColor Yellow
        Write-Host ("=" * 50) -ForegroundColor Gray
        
        if (Set-LoadBalancingStrategy -Strategy $strategy) {
            $result = Test-LoadBalancing -Model $testModel -Strategy $strategy -RequestCount 6
            $results += $result
        }
    }
    
    # Summary
    Write-Host "`n📈 SUMMARY" -ForegroundColor Green
    Write-Host "==========" -ForegroundColor Green
    
    foreach ($result in $results) {
        Write-Host "`n🎯 $($result.Strategy.ToUpper()):" -ForegroundColor Yellow
        Write-Host "Average time: $([Math]::Round($result.AvgResponseTime, 2))s" -ForegroundColor White
        foreach ($server in $result.ServerDistribution.Keys | Sort-Object) {
            Write-Host "  • $server`: $($result.ServerDistribution[$server]) requests" -ForegroundColor Gray
        }
    }
    
    # Set optimal strategy
    $fastest = $results | Sort-Object -Property AvgResponseTime | Select-Object -First 1
    Write-Host "`n🏆 Optimal: $($fastest.Strategy) ($([Math]::Round($fastest.AvgResponseTime, 2))s avg)" -ForegroundColor Green
    
    Set-LoadBalancingStrategy -Strategy $fastest.Strategy
    Write-Host "`n✅ Load balancing test complete!" -ForegroundColor Green
    
}
catch {
    Write-Host "`n❌ Test failed: $($_.Exception.Message)" -ForegroundColor Red
}
