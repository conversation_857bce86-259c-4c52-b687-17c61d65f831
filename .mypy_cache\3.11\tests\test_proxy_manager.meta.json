{"data_mtime": 1753377585, "dep_lines": [5, 8, 9, 3, 4, 6, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["unittest.mock", "llm_proxy_server.proxy_manager", "llm_proxy_server.models", "pytest", "json", "pathlib", "builtins", "_frozen_importlib", "_pytest", "_pytest._code", "_pytest._code.code", "_pytest.config", "_pytest.fixtures", "_pytest.mark", "_pytest.mark.structures", "_pytest.raises", "abc", "llm_proxy_server", "llm_proxy_server.config", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "re", "types", "typing", "typing_extensions", "unittest"], "hash": "78e5765316c3c1cb881d519f8c0df2bab42731b5", "id": "tests.test_proxy_manager", "ignore_all": false, "interface_hash": "3f2a308984de9069448dc18b29f9c4b62258dbf8", "mtime": 1753378231, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\llm_proxy_server\\llm-proxy-server\\tests\\test_proxy_manager.py", "plugin_data": null, "size": 14710, "suppressed": [], "version_id": "1.15.0"}