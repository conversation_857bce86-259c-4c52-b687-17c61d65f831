# PowerShell script to stop the local LLM stack

Write-Host "🛑 Stopping Local LLM Stack" -ForegroundColor Red
Write-Host "============================" -ForegroundColor Red

try {
    # Stop and remove containers
    Write-Host "`n📦 Stopping containers..." -ForegroundColor Yellow
    docker-compose -f docker-compose.local.yml down
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Stack stopped successfully!" -ForegroundColor Green
        
        Write-Host "`n📊 Remaining containers:" -ForegroundColor Cyan
        docker ps --filter "name=llm-proxy-server" --filter "name=open-webui"
        
        Write-Host "`n💾 Data volumes preserved:" -ForegroundColor Cyan
        docker volume ls --filter "name=open-webui"
        
        Write-Host "`n🔄 To restart the stack:" -ForegroundColor Gray
        Write-Host ".\start-local-stack.ps1" -ForegroundColor White
    } else {
        Write-Host "❌ Error stopping stack" -ForegroundColor Red
    }
}
catch {
    Write-Host "❌ Error: $($_.Exception.Message)" -ForegroundColor Red
}
