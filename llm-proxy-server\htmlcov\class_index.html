<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">81%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.0">coverage.py v7.10.0</a>,
            created at 2025-07-24 13:32 -0400
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1___init___py.html">llm_proxy_server\__init__.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html#t11">llm_proxy_server\auth.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html#t11"><data value='AuthManager'>AuthManager</data></a></td>
                <td>49</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="47 49">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html">llm_proxy_server\auth.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>12</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="12 12">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_py.html#t9">llm_proxy_server\config.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_py.html#t9"><data value='Settings'>Settings</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_py.html">llm_proxy_server\config.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>22</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="22 22">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t13">llm_proxy_server\load_balancer.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html#t13"><data value='LoadBalancer'>LoadBalancer</data></a></td>
                <td>67</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="63 67">94%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html">llm_proxy_server\load_balancer.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html">llm_proxy_server\main.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>140</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="117 140">84%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html#t8">llm_proxy_server\models.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html#t8"><data value='ChatMessage'>ChatMessage</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html#t14">llm_proxy_server\models.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html#t14"><data value='ChatRequest'>ChatRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html#t23">llm_proxy_server\models.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html#t23"><data value='ChatResponse'>ChatResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html#t36">llm_proxy_server\models.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html#t36"><data value='GenerateRequest'>GenerateRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html#t50">llm_proxy_server\models.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html#t50"><data value='GenerateResponse'>GenerateResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html#t64">llm_proxy_server\models.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html#t64"><data value='EmbedRequest'>EmbedRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html#t71">llm_proxy_server\models.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html#t71"><data value='EmbedResponse'>EmbedResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html#t75">llm_proxy_server\models.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html#t75"><data value='ShowRequest'>ShowRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html#t79">llm_proxy_server\models.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html#t79"><data value='ShowResponse'>ShowResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html#t86">llm_proxy_server\models.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html#t86"><data value='ModelInfo'>ModelInfo</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html#t95">llm_proxy_server\models.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html#t95"><data value='ModelsResponse'>ModelsResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html#t99">llm_proxy_server\models.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html#t99"><data value='HostConfig'>HostConfig</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html#t108">llm_proxy_server\models.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html#t108"><data value='ProxyStatus'>ProxyStatus</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html#t117">llm_proxy_server\models.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html#t117"><data value='HealthResponse'>HealthResponse</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html">llm_proxy_server\models.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>91</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="91 91">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t12">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html#t12"><data value='MetricsManager'>MetricsManager</data></a></td>
                <td>89</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="85 89">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html">llm_proxy_server\monitoring.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>19</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="19 19">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t29">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html#t29"><data value='ProxyManager'>ProxyManager</data></a></td>
                <td>230</td>
                <td>116</td>
                <td>0</td>
                <td class="right" data-ratio="114 230">50%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html">llm_proxy_server\proxy_manager.py</a></td>
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>27</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="27 27">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>771</td>
                <td>149</td>
                <td>0</td>
                <td class="right" data-ratio="622 771">81%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.0">coverage.py v7.10.0</a>,
            created at 2025-07-24 13:32 -0400
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
