{".class": "MypyFile", "_fullname": "_pytest.raises", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "<subclass of \"GenericAlias\" and \"BaseException\">": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["types.GenericAlias", "builtins.BaseException"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.raises.<subclass of \"GenericAlias\" and \"BaseException\">", "name": "<subclass of \"GenericAlias\" and \"BaseException\">", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_intersection"], "fullname": "_pytest.raises.<subclass of \"GenericAlias\" and \"BaseException\">", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.raises", "mro": ["_pytest.raises.<subclass of \"GenericAlias\" and \"BaseException\">", "types.GenericAlias", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "<subclass of \"GenericAlias\" and \"Exception\">": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["types.GenericAlias", "builtins.Exception"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.raises.<subclass of \"GenericAlias\" and \"Exception\">", "name": "<subclass of \"GenericAlias\" and \"Exception\">", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_intersection"], "fullname": "_pytest.raises.<subclass of \"GenericAlias\" and \"Exception\">", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.raises", "mro": ["_pytest.raises.<subclass of \"GenericAlias\" and \"Exception\">", "types.GenericAlias", "builtins.Exception", "builtins.BaseException", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "<subclass of \"GenericAlias\" and \"type\">": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["types.GenericAlias", "builtins.type"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.raises.<subclass of \"GenericAlias\" and \"type\">", "name": "<subclass of \"GenericAlias\" and \"type\">", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_intersection"], "fullname": "_pytest.raises.<subclass of \"GenericAlias\" and \"type\">", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.raises", "mro": ["_pytest.raises.<subclass of \"GenericAlias\" and \"type\">", "types.GenericAlias", "builtins.type", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": null, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ABC": {".class": "SymbolTableNode", "cross_ref": "abc.ABC", "kind": "Gdef"}, "AbstractRaises": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [["matches", 1]], "alt_promote": null, "bases": ["abc.ABC"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.raises.AbstractRaises", "name": "AbstractRaises", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.AbstractRaises", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_abstract"], "fullname": "_pytest.raises.AbstractRaises", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "_pytest.raises", "mro": ["_pytest.raises.AbstractRaises", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 3], "arg_names": ["self", "match", "check"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.raises.AbstractRaises.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 3], "arg_names": ["self", "match", "check"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.AbstractRaises", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.AbstractRaises"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.AbstractRaises", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of AbstractRaises", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_check": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.raises.AbstractRaises._check_check", "name": "_check_check", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.AbstractRaises._check_check", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.AbstractRaises"}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.AbstractRaises._check_check", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_check of AbstractRaises", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.AbstractRaises._check_check", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}}}, "_check_match": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "e"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.raises.AbstractRaises._check_match", "name": "_check_match", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "e"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.AbstractRaises", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.AbstractRaises"}, "builtins.BaseException"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_match of AbstractRaises", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_fail_reason": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.raises.AbstractRaises._fail_reason", "name": "_fail_reason", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_nested": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.raises.AbstractRaises._nested", "name": "_nested", "type": "builtins.bool"}}, "_parse_exc": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "exc", "expected"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.raises.AbstractRaises._parse_exc", "name": "_parse_exc", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "exc", "expected"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.AbstractRaises", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.AbstractRaises"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.AbstractRaises._parse_exc", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}, "types.GenericAlias"], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_exc of AbstractRaises", "ret_type": {".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.AbstractRaises._parse_exc", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.AbstractRaises._parse_exc", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}}}, "check": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.raises.AbstractRaises.check", "name": "check", "type": {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.AbstractRaises", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "fail_reason": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_property", "is_decorated"], "fullname": "_pytest.raises.AbstractRaises.fail_reason", "name": "fail_reason", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.AbstractRaises", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.AbstractRaises"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fail_reason of AbstractRaises", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_property", "is_ready", "is_inferred"], "fullname": "_pytest.raises.AbstractRaises.fail_reason", "name": "fail_reason", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.AbstractRaises", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.AbstractRaises"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "fail_reason of AbstractRaises", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "is_baseexception": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.raises.AbstractRaises.is_baseexception", "name": "is_baseexception", "type": "builtins.bool"}}, "match": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.raises.AbstractRaises.match", "name": "match", "type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "matches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 1, "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated", "is_trivial_body"], "fullname": "_pytest.raises.AbstractRaises.matches", "name": "matches", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.AbstractRaises.matches", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.AbstractRaises"}, "builtins.BaseException"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "matches of AbstractRaises", "ret_type": "builtins.bool", "type_guard": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.AbstractRaises.matches", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.AbstractRaises.matches", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "_pytest.raises.AbstractRaises.matches", "name": "matches", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.AbstractRaises.matches", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.AbstractRaises"}, "builtins.BaseException"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "matches of AbstractRaises", "ret_type": "builtins.bool", "type_guard": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.AbstractRaises.matches", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.AbstractRaises.matches", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}}}}, "rawmatch": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.raises.AbstractRaises.rawmatch", "name": "rawmatch", "type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.AbstractRaises.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.AbstractRaises", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.AbstractRaises"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["BaseExcT_co"], "typeddict_type": null}}, "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseExcT_1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "name": "BaseExcT_1", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}, "BaseExcT_2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "name": "BaseExcT_2", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}, "BaseExcT_co": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "name": "BaseExcT_co", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}}, "BaseExcT_co_default": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": "builtins.BaseException", "fullname": "_pytest.raises.BaseExcT_co_default", "name": "BaseExcT_co_default", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "E": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": "builtins.BaseException", "fullname": "_pytest.raises.E", "name": "E", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}, "ExcT_1": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "name": "ExcT_1", "upper_bound": "builtins.Exception", "values": [], "variance": 0}}, "ExcT_2": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeVarExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_2", "name": "ExcT_2", "upper_bound": "builtins.Exception", "values": [], "variance": 0}}, "ExceptionInfo": {".class": "SymbolTableNode", "cross_ref": "_pytest._code.code.ExceptionInfo", "kind": "Gdef"}, "Generic": {".class": "SymbolTableNode", "cross_ref": "typing.Generic", "kind": "Gdef"}, "Literal": {".class": "SymbolTableNode", "cross_ref": "typing.Literal", "kind": "Gdef"}, "NotChecked": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.raises.NotChecked", "name": "NotChecked", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "_pytest.raises.NotChecked", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.raises", "mro": ["_pytest.raises.NotChecked", "builtins.object"], "names": {".class": "SymbolTable"}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.NotChecked.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.raises.NotChecked", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "P": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "ParamSpecExpr", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.P", "name": "P", "upper_bound": "builtins.object", "variance": 0}}, "ParamSpec": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.ParamSpec", "kind": "Gdef"}, "Pattern": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON>", "kind": "Gdef"}, "PytestWarning": {".class": "SymbolTableNode", "cross_ref": "_pytest.warning_types.PytestWarning", "kind": "Gdef"}, "RaisesExc": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.BaseExcT_co_default", "id": 1, "name": "BaseExcT_co_default", "namespace": "_pytest.raises.RaisesExc", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.AbstractRaises"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.raises.RaisesExc", "name": "RaisesExc", "type_vars": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.BaseExcT_co_default", "id": 1, "name": "BaseExcT_co_default", "namespace": "_pytest.raises.RaisesExc", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "_pytest.raises.RaisesExc", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "_pytest.raises", "mro": ["_pytest.raises.RaisesExc", "_pytest.raises.AbstractRaises", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.raises.RaisesExc.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.BaseExcT_co_default", "id": 1, "name": "BaseExcT_co_default", "namespace": "_pytest.raises.RaisesExc", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of RaisesExc", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.BaseExcT_co_default", "id": 1, "name": "BaseExcT_co_default", "namespace": "_pytest.raises.RaisesExc", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest._code.code.ExceptionInfo"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.raises.RaisesExc.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.BaseExcT_co_default", "id": 1, "name": "BaseExcT_co_default", "namespace": "_pytest.raises.RaisesExc", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__exit__ of RaisesExc", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "_pytest.raises.RaisesExc.__init__", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1, 5, 5], "arg_names": [null, null, "match", "check"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "_pytest.raises.RaisesExc.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1, 5, 5], "arg_names": [null, null, "match", "check"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.BaseExcT_co_default", "id": 1, "name": "BaseExcT_co_default", "namespace": "_pytest.raises.RaisesExc", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.BaseExcT_co_default", "id": 1, "name": "BaseExcT_co_default", "namespace": "_pytest.raises.RaisesExc", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.BaseExcT_co_default", "id": 1, "name": "BaseExcT_co_default", "namespace": "_pytest.raises.RaisesExc", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.BaseExcT_co_default", "id": 1, "name": "BaseExcT_co_default", "namespace": "_pytest.raises.RaisesExc", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesExc", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 5, 5], "arg_names": [null, null, "match", "check"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.raises.RaisesExc.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": [null, null, "match", "check"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.BaseExcT_co_default", "id": 1, "name": "BaseExcT_co_default", "namespace": "_pytest.raises.RaisesExc", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.BaseExcT_co_default", "id": 1, "name": "BaseExcT_co_default", "namespace": "_pytest.raises.RaisesExc", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.BaseExcT_co_default", "id": 1, "name": "BaseExcT_co_default", "namespace": "_pytest.raises.RaisesExc", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.BaseExcT_co_default", "id": 1, "name": "BaseExcT_co_default", "namespace": "_pytest.raises.RaisesExc", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesExc", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.raises.RaisesExc.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": [null, null, "match", "check"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.BaseExcT_co_default", "id": 1, "name": "BaseExcT_co_default", "namespace": "_pytest.raises.RaisesExc", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.BaseExcT_co_default", "id": 1, "name": "BaseExcT_co_default", "namespace": "_pytest.raises.RaisesExc", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.BaseExcT_co_default", "id": 1, "name": "BaseExcT_co_default", "namespace": "_pytest.raises.RaisesExc", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.BaseExcT_co_default", "id": 1, "name": "BaseExcT_co_default", "namespace": "_pytest.raises.RaisesExc", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesExc", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3, 5], "arg_names": [null, "match", "check"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.raises.RaisesExc.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5], "arg_names": [null, "match", "check"], "arg_types": [{".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.BaseException"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesExc", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.raises.RaisesExc.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3, 5], "arg_names": [null, "match", "check"], "arg_types": [{".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.BaseException"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesExc", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 3], "arg_names": [null, "check"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.raises.RaisesExc.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": [null, "check"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.BaseExcT_co_default", "id": 1, "name": "BaseExcT_co_default", "namespace": "_pytest.raises.RaisesExc", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.BaseException"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesExc", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.raises.RaisesExc.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": [null, "check"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.BaseExcT_co_default", "id": 1, "name": "BaseExcT_co_default", "namespace": "_pytest.raises.RaisesExc", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.BaseException"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesExc", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 5, 5], "arg_names": [null, null, "match", "check"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.BaseExcT_co_default", "id": 1, "name": "BaseExcT_co_default", "namespace": "_pytest.raises.RaisesExc", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.BaseExcT_co_default", "id": 1, "name": "BaseExcT_co_default", "namespace": "_pytest.raises.RaisesExc", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.BaseExcT_co_default", "id": 1, "name": "BaseExcT_co_default", "namespace": "_pytest.raises.RaisesExc", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.BaseExcT_co_default", "id": 1, "name": "BaseExcT_co_default", "namespace": "_pytest.raises.RaisesExc", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesExc", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 3, 5], "arg_names": [null, "match", "check"], "arg_types": [{".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.BaseException"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesExc", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 3], "arg_names": [null, "check"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.BaseExcT_co_default", "id": 1, "name": "BaseExcT_co_default", "namespace": "_pytest.raises.RaisesExc", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.BaseException"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesExc", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}]}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.raises.RaisesExc.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.BaseExcT_co_default", "id": 1, "name": "BaseExcT_co_default", "namespace": "_pytest.raises.RaisesExc", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of RaisesExc", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.raises.RaisesExc._check_type", "name": "_check_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.BaseExcT_co_default", "id": 1, "name": "BaseExcT_co_default", "namespace": "_pytest.raises.RaisesExc", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, "builtins.BaseException"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_type of RaisesExc", "ret_type": "builtins.bool", "type_guard": {".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.BaseExcT_co_default", "id": 1, "name": "BaseExcT_co_default", "namespace": "_pytest.raises.RaisesExc", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_just_propagate": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.raises.RaisesExc._just_propagate", "name": "_just_propagate", "type": "builtins.bool"}}, "excinfo": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.raises.RaisesExc.excinfo", "name": "excinfo", "type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.BaseExcT_co_default", "id": 1, "name": "BaseExcT_co_default", "namespace": "_pytest.raises.RaisesExc", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest._code.code.ExceptionInfo"}}}, "expected_exceptions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.raises.RaisesExc.expected_exceptions", "name": "expected_exceptions", "type": {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.BaseExcT_co_default", "id": 1, "name": "BaseExcT_co_default", "namespace": "_pytest.raises.RaisesExc", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "matches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.raises.RaisesExc.matches", "name": "matches", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.BaseExcT_co_default", "id": 1, "name": "BaseExcT_co_default", "namespace": "_pytest.raises.RaisesExc", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "matches of RaisesExc", "ret_type": "builtins.bool", "type_guard": {".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.BaseExcT_co_default", "id": 1, "name": "BaseExcT_co_default", "namespace": "_pytest.raises.RaisesExc", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.RaisesExc.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.BaseExcT_co_default", "id": 1, "name": "BaseExcT_co_default", "namespace": "_pytest.raises.RaisesExc", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["BaseExcT_co_default"], "typeddict_type": null}}, "RaisesGroup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}], "extra_attrs": null, "type_ref": "_pytest.raises.AbstractRaises"}], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.raises.RaisesGroup", "name": "RaisesGroup", "type_vars": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}]}, "deletable_attributes": [], "deprecated": null, "flags": ["is_final"], "fullname": "_pytest.raises.RaisesGroup", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "_pytest.raises", "mro": ["_pytest.raises.RaisesGroup", "_pytest.raises.AbstractRaises", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__enter__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "_pytest.raises.RaisesGroup.__enter__", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "_pytest.raises.RaisesGroup.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of RaisesGroup", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}], "extra_attrs": null, "type_ref": "_pytest._code.code.ExceptionInfo"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.raises.RaisesGroup.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__enter__#0", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of RaisesGroup", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__enter__#0", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.ExceptionGroup"}], "extra_attrs": null, "type_ref": "_pytest._code.code.ExceptionInfo"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__enter__#0", "upper_bound": "builtins.Exception", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.raises.RaisesGroup.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__enter__#0", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of RaisesGroup", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__enter__#0", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.ExceptionGroup"}], "extra_attrs": null, "type_ref": "_pytest._code.code.ExceptionInfo"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__enter__#0", "upper_bound": "builtins.Exception", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.raises.RaisesGroup.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__enter__#1", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of RaisesGroup", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__enter__#1", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}], "extra_attrs": null, "type_ref": "_pytest._code.code.ExceptionInfo"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__enter__#1", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.raises.RaisesGroup.__enter__", "name": "__enter__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__enter__#1", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of RaisesGroup", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__enter__#1", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}], "extra_attrs": null, "type_ref": "_pytest._code.code.ExceptionInfo"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__enter__#1", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__enter__#0", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of RaisesGroup", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__enter__#0", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.ExceptionGroup"}], "extra_attrs": null, "type_ref": "_pytest._code.code.ExceptionInfo"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__enter__#0", "upper_bound": "builtins.Exception", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__enter__#1", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__enter__ of RaisesGroup", "ret_type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__enter__#1", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}], "extra_attrs": null, "type_ref": "_pytest._code.code.ExceptionInfo"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__enter__#1", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}]}}}, "__exit__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.raises.RaisesGroup.__exit__", "name": "__exit__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": [null, null, null, null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["types.TracebackType", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__exit__ of RaisesGroup", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "_pytest.raises.RaisesGroup.__init__", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 5, 5, 5, 5], "arg_names": [null, null, "other_exceptions", "allow_unwrapped", "flatten_subgroups", "match", "check"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "_pytest.raises.RaisesGroup.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5, 5, 5, 5], "arg_names": [null, null, "other_exceptions", "allow_unwrapped", "flatten_subgroups", "match", "check"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__", "upper_bound": "builtins.Exception", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -2, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -3, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -2, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -2, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -3, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -2, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -2, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -3, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}], "uses_pep604_syntax": true}, "builtins.bool", "builtins.bool", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -2, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.ExceptionGroup"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__", "upper_bound": "builtins.Exception", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -2, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -3, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 3, 5], "arg_names": [null, null, "allow_unwrapped", "flatten_subgroups"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.raises.RaisesGroup.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 5], "arg_names": [null, null, "allow_unwrapped", "flatten_subgroups"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.raises.RaisesGroup.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 3, 5], "arg_names": [null, null, "allow_unwrapped", "flatten_subgroups"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 3, 5, 5], "arg_names": [null, null, "other_exceptions", "flatten_subgroups", "match", "check"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.raises.RaisesGroup.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 3, 5, 5], "arg_names": [null, null, "other_exceptions", "flatten_subgroups", "match", "check"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.raises.RaisesGroup.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 3, 5, 5], "arg_names": [null, null, "other_exceptions", "flatten_subgroups", "match", "check"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 5, 5], "arg_names": [null, null, "other_exceptions", "match", "check"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.raises.RaisesGroup.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5, 5], "arg_names": [null, null, "other_exceptions", "match", "check"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#2", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#2", "upper_bound": "builtins.Exception", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#2", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#2", "upper_bound": "builtins.Exception", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#2", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#2", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.ExceptionGroup"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#2", "upper_bound": "builtins.Exception", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.raises.RaisesGroup.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5, 5], "arg_names": [null, null, "other_exceptions", "match", "check"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#2", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#2", "upper_bound": "builtins.Exception", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#2", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#2", "upper_bound": "builtins.Exception", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#2", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#2", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.ExceptionGroup"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#2", "upper_bound": "builtins.Exception", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 5, 5], "arg_names": [null, null, "other_exceptions", "match", "check"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.raises.RaisesGroup.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5, 5], "arg_names": [null, null, "other_exceptions", "match", "check"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_2", "id": -1, "name": "ExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#3", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.ExceptionGroup"}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_2", "id": -1, "name": "ExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#3", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_2", "id": -1, "name": "ExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#3", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_2", "id": -1, "name": "ExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#3", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.ExceptionGroup"}], "extra_attrs": null, "type_ref": "builtins.ExceptionGroup"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_2", "id": -1, "name": "ExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#3", "upper_bound": "builtins.Exception", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.raises.RaisesGroup.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5, 5], "arg_names": [null, null, "other_exceptions", "match", "check"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_2", "id": -1, "name": "ExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#3", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.ExceptionGroup"}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_2", "id": -1, "name": "ExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#3", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_2", "id": -1, "name": "ExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#3", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_2", "id": -1, "name": "ExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#3", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.ExceptionGroup"}], "extra_attrs": null, "type_ref": "builtins.ExceptionGroup"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_2", "id": -1, "name": "ExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#3", "upper_bound": "builtins.Exception", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 5, 5], "arg_names": [null, null, "other_exceptions", "match", "check"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.raises.RaisesGroup.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5, 5], "arg_names": [null, null, "other_exceptions", "match", "check"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_2", "id": -2, "name": "ExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.ExceptionGroup"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_2", "id": -2, "name": "ExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_2", "id": -2, "name": "ExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_2", "id": -2, "name": "ExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.ExceptionGroup"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.ExceptionGroup"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_2", "id": -2, "name": "ExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.raises.RaisesGroup.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5, 5], "arg_names": [null, null, "other_exceptions", "match", "check"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_2", "id": -2, "name": "ExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.ExceptionGroup"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_2", "id": -2, "name": "ExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_2", "id": -2, "name": "ExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_2", "id": -2, "name": "ExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.ExceptionGroup"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.ExceptionGroup"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_2", "id": -2, "name": "ExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 5, 5], "arg_names": [null, null, "other_exceptions", "match", "check"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.raises.RaisesGroup.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5, 5], "arg_names": [null, null, "other_exceptions", "match", "check"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#5", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#5", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#5", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#5", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#5", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#5", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#5", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.raises.RaisesGroup.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5, 5], "arg_names": [null, null, "other_exceptions", "match", "check"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#5", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#5", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#5", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#5", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#5", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#5", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#5", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 5, 5], "arg_names": [null, null, "other_exceptions", "match", "check"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.raises.RaisesGroup.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5, 5], "arg_names": [null, null, "other_exceptions", "match", "check"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -1, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#6", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -1, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#6", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -1, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#6", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -1, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#6", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -1, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#6", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.raises.RaisesGroup.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5, 5], "arg_names": [null, null, "other_exceptions", "match", "check"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -1, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#6", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -1, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#6", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -1, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#6", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -1, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#6", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -1, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#6", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 5, 5], "arg_names": [null, null, "other_exceptions", "match", "check"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.raises.RaisesGroup.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5, 5], "arg_names": [null, null, "other_exceptions", "match", "check"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -2, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -2, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -2, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -2, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -2, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.raises.RaisesGroup.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 5, 5], "arg_names": [null, null, "other_exceptions", "match", "check"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -2, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -2, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -2, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -2, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -2, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 3, 5], "arg_names": [null, null, "allow_unwrapped", "flatten_subgroups"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 2, 3, 5, 5], "arg_names": [null, null, "other_exceptions", "flatten_subgroups", "match", "check"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}], "uses_pep604_syntax": true}, {".class": "LiteralType", "fallback": "builtins.bool", "value": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 2, 5, 5], "arg_names": [null, null, "other_exceptions", "match", "check"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#2", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#2", "upper_bound": "builtins.Exception", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#2", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#2", "upper_bound": "builtins.Exception", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#2", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#2", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.ExceptionGroup"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#2", "upper_bound": "builtins.Exception", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 2, 5, 5], "arg_names": [null, null, "other_exceptions", "match", "check"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_2", "id": -1, "name": "ExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#3", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.ExceptionGroup"}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_2", "id": -1, "name": "ExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#3", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_2", "id": -1, "name": "ExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#3", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_2", "id": -1, "name": "ExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#3", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.ExceptionGroup"}], "extra_attrs": null, "type_ref": "builtins.ExceptionGroup"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_2", "id": -1, "name": "ExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#3", "upper_bound": "builtins.Exception", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 2, 5, 5], "arg_names": [null, null, "other_exceptions", "match", "check"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_2", "id": -2, "name": "ExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.ExceptionGroup"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_2", "id": -2, "name": "ExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_2", "id": -2, "name": "ExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_2", "id": -2, "name": "ExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.ExceptionGroup"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.ExceptionGroup"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_2", "id": -2, "name": "ExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#4", "upper_bound": "builtins.Exception", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 2, 5, 5], "arg_names": [null, null, "other_exceptions", "match", "check"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#5", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#5", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#5", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#5", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#5", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#5", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#5", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 2, 5, 5], "arg_names": [null, null, "other_exceptions", "match", "check"], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -1, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#6", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -1, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#6", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -1, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#6", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -1, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#6", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -1, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#6", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 2, 5, 5], "arg_names": [null, null, "other_exceptions", "match", "check"], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -2, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -2, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -2, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -2, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of RaisesGroup", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -2, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup.__init__#7", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}]}}}, "__repr__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": [null], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.raises.RaisesGroup.__repr__", "name": "__repr__", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__repr__ of RaisesGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_check_exceptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "_pytest.raises.RaisesGroup._check_exceptions", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "_exception", "actual_exceptions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "_pytest.raises.RaisesGroup._check_exceptions", "name": "_check_exceptions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "_exception", "actual_exceptions"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, "builtins.BaseException", {".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_exceptions of RaisesGroup", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "_exception", "actual_exceptions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.raises.RaisesGroup._check_exceptions", "name": "_check_exceptions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "_exception", "actual_exceptions"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup._check_exceptions#0", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, "builtins.Exception", {".class": "Instance", "args": ["builtins.Exception"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_exceptions of RaisesGroup", "ret_type": "builtins.bool", "type_guard": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup._check_exceptions#0", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.ExceptionGroup"}, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup._check_exceptions#0", "upper_bound": "builtins.Exception", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.raises.RaisesGroup._check_exceptions", "name": "_check_exceptions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "_exception", "actual_exceptions"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup._check_exceptions#0", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, "builtins.Exception", {".class": "Instance", "args": ["builtins.Exception"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_exceptions of RaisesGroup", "ret_type": "builtins.bool", "type_guard": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup._check_exceptions#0", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.ExceptionGroup"}, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup._check_exceptions#0", "upper_bound": "builtins.Exception", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "_exception", "actual_exceptions"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.raises.RaisesGroup._check_exceptions", "name": "_check_exceptions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "_exception", "actual_exceptions"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup._check_exceptions#1", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, "builtins.BaseException", {".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_exceptions of RaisesGroup", "ret_type": "builtins.bool", "type_guard": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup._check_exceptions#1", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup._check_exceptions#1", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.raises.RaisesGroup._check_exceptions", "name": "_check_exceptions", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "_exception", "actual_exceptions"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup._check_exceptions#1", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, "builtins.BaseException", {".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_exceptions of RaisesGroup", "ret_type": "builtins.bool", "type_guard": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup._check_exceptions#1", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup._check_exceptions#1", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "_exception", "actual_exceptions"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup._check_exceptions#0", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, "builtins.Exception", {".class": "Instance", "args": ["builtins.Exception"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_exceptions of RaisesGroup", "ret_type": "builtins.bool", "type_guard": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup._check_exceptions#0", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.ExceptionGroup"}, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup._check_exceptions#0", "upper_bound": "builtins.Exception", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "_exception", "actual_exceptions"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup._check_exceptions#1", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, "builtins.BaseException", {".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_exceptions of RaisesGroup", "ret_type": "builtins.bool", "type_guard": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup._check_exceptions#1", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup._check_exceptions#1", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}]}}}, "_check_expected": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["expected_type", "exception"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "_pytest.raises.RaisesGroup._check_expected", "name": "_check_expected", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["expected_type", "exception"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, {".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}], "uses_pep604_syntax": true}, "builtins.BaseException"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_expected of RaisesGroup", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "_pytest.raises.RaisesGroup._check_expected", "name": "_check_expected", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["expected_type", "exception"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, {".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}], "uses_pep604_syntax": true}, "builtins.BaseException"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_expected of RaisesGroup", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_parse_excgroup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "exc", "expected"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.raises.RaisesGroup._parse_excgroup", "name": "_parse_excgroup", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "exc", "expected"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}}, "types.GenericAlias", {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup._parse_excgroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -2, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup._parse_excgroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}], "uses_pep604_syntax": true}, "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_excgroup of RaisesGroup", "ret_type": {".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup._parse_excgroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -2, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup._parse_excgroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup._parse_excgroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}, {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_2", "id": -2, "name": "BaseExcT_2", "namespace": "_pytest.raises.RaisesGroup._parse_excgroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}}}, "_repr_expected": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["e"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "_pytest.raises.RaisesGroup._repr_expected", "name": "_repr_expected", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["e"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "_pytest.raises.AbstractRaises"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_repr_expected of RaisesGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_staticmethod", "is_ready", "is_inferred"], "fullname": "_pytest.raises.RaisesGroup._repr_expected", "name": "_repr_expected", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["e"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "_pytest.raises.AbstractRaises"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_repr_expected of RaisesGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_unroll_exceptions": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exceptions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.raises.RaisesGroup._unroll_exceptions", "name": "_unroll_exceptions", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exceptions"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_unroll_exceptions of RaisesGroup", "ret_type": {".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "typing.Sequence"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "allow_unwrapped": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "_pytest.raises.RaisesGroup.allow_unwrapped", "name": "allow_unwrapped", "type": "builtins.bool"}}, "excinfo": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.raises.RaisesGroup.excinfo", "name": "excinfo", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}], "extra_attrs": null, "type_ref": "_pytest._code.code.ExceptionInfo"}}}, "expected_exceptions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.raises.RaisesGroup.expected_exceptions", "name": "expected_exceptions", "type": {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, {".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "expected_type": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.raises.RaisesGroup.expected_type", "name": "expected_type", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "expected_type of RaisesGroup", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "flatten_subgroups": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.raises.RaisesGroup.flatten_subgroups", "name": "flatten_subgroups", "type": "builtins.bool"}}, "matches": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "_pytest.raises.RaisesGroup.matches", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "_pytest.raises.RaisesGroup.matches", "name": "matches", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "matches of RaisesGroup", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.raises.RaisesGroup.matches", "name": "matches", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.matches#0", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "matches of RaisesGroup", "ret_type": "builtins.bool", "type_guard": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.matches#0", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.ExceptionGroup"}, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.matches#0", "upper_bound": "builtins.Exception", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.raises.RaisesGroup.matches", "name": "matches", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.matches#0", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "matches of RaisesGroup", "ret_type": "builtins.bool", "type_guard": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.matches#0", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.ExceptionGroup"}, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.matches#0", "upper_bound": "builtins.Exception", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.raises.RaisesGroup.matches", "name": "matches", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.matches#1", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "matches of RaisesGroup", "ret_type": "builtins.bool", "type_guard": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.matches#1", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.matches#1", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.raises.RaisesGroup.matches", "name": "matches", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.matches#1", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "matches of RaisesGroup", "ret_type": "builtins.bool", "type_guard": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.matches#1", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.matches#1", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.matches#0", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "matches of RaisesGroup", "ret_type": "builtins.bool", "type_guard": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.matches#0", "upper_bound": "builtins.Exception", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.ExceptionGroup"}, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ExcT_1", "id": -1, "name": "ExcT_1", "namespace": "_pytest.raises.RaisesGroup.matches#0", "upper_bound": "builtins.Exception", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "exception"], "arg_types": [{".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.matches#1", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, {".class": "UnionType", "items": ["builtins.BaseException", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "matches of RaisesGroup", "ret_type": "builtins.bool", "type_guard": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.matches#1", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "builtins.BaseExceptionGroup"}, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.RaisesGroup.matches#1", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}]}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.RaisesGroup.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": {".class": "Instance", "args": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_co", "id": 1, "name": "BaseExcT_co", "namespace": "_pytest.raises.RaisesGroup", "upper_bound": "builtins.BaseException", "values": [], "variance": 1}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesGroup"}, "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": ["BaseExcT_co"], "typeddict_type": null}}, "ResultHolder": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.raises.ResultHolder", "name": "ResultHolder", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "_pytest.raises.ResultHolder", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.raises", "mro": ["_pytest.raises.ResultHolder", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "expected_exceptions", "actual_exceptions"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.raises.ResultHolder.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "expected_exceptions", "actual_exceptions"], "arg_types": ["_pytest.raises.ResultHolder", {".class": "Instance", "args": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "_pytest.raises.AbstractRaises"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "typing.Sequence"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ResultHolder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "expected", "actual"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.raises.ResultHolder.get_result", "name": "get_result", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "expected", "actual"], "arg_types": ["_pytest.raises.ResultHolder", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_result of ResultHolder", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "has_result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "expected", "actual"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.raises.ResultHolder.has_result", "name": "has_result", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "expected", "actual"], "arg_types": ["_pytest.raises.ResultHolder", "builtins.int", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "has_result of ResultHolder", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "no_match_for_actual": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "actual"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.raises.ResultHolder.no_match_for_actual", "name": "no_match_for_actual", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "actual"], "arg_types": ["_pytest.raises.ResultHolder", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "no_match_for_actual of ResultHolder", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "no_match_for_expected": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "expected"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.raises.ResultHolder.no_match_for_expected", "name": "no_match_for_expected", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "expected"], "arg_types": ["_pytest.raises.ResultHolder", {".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "no_match_for_expected of ResultHolder", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "results": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["explicit_self_type", "is_ready", "is_inferred"], "fullname": "_pytest.raises.ResultHolder.results", "name": "results", "type": {".class": "Instance", "args": [{".class": "Instance", "args": [{".class": "UnionType", "items": ["builtins.str", {".class": "TypeType", "item": "_pytest.raises.NotChecked"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "extra_attrs": null, "type_ref": "builtins.list"}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "set_result": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "expected", "actual", "result"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.raises.ResultHolder.set_result", "name": "set_result", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "expected", "actual", "result"], "arg_types": ["_pytest.raises.ResultHolder", "builtins.int", "builtins.int", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_result of ResultHolder", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.ResultHolder.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.raises.ResultHolder", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TypeGuard": {".class": "SymbolTableNode", "cross_ref": "typing.TypeGuard", "kind": "Gdef"}, "TypeVar": {".class": "SymbolTableNode", "cross_ref": "typing_extensions.TypeVar", "kind": "Gdef"}, "_REGEX_NO_FLAGS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "_pytest.raises._REGEX_NO_FLAGS", "name": "_REGEX_NO_FLAGS", "type": "builtins.int"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.raises.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.raises.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.raises.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.raises.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.raises.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.raises.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_check_raw_type": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["expected_type", "exception"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.raises._check_raw_type", "name": "_check_raw_type", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["expected_type", "exception"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "Instance", "args": [{".class": "TypeType", "item": "builtins.BaseException"}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "builtins.BaseException"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_check_raw_type", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_exception_type_name": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["e"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.raises._exception_type_name", "name": "_exception_type_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["e"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": "builtins.BaseException"}, {".class": "Instance", "args": [{".class": "TypeType", "item": "builtins.BaseException"}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_exception_type_name", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_match_pattern": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["match"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.raises._match_pattern", "name": "_match_pattern", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["match"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_match_pattern", "ret_type": {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "abstractmethod": {".class": "SymbolTableNode", "cross_ref": "abc.abstractmethod", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "backquote": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["s"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.raises.backquote", "name": "backquote", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["s"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "backquote", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "cast": {".class": "SymbolTableNode", "cross_ref": "typing.cast", "kind": "Gdef"}, "fail": {".class": "SymbolTableNode", "cross_ref": "_pytest.outcomes.fail", "kind": "Gdef"}, "final": {".class": "SymbolTableNode", "cross_ref": "typing.final", "kind": "Gdef"}, "get_args": {".class": "SymbolTableNode", "cross_ref": "typing.get_args", "kind": "Gdef"}, "get_origin": {".class": "SymbolTableNode", "cross_ref": "typing.get_origin", "kind": "Gdef"}, "indent": {".class": "SymbolTableNode", "cross_ref": "textwrap.indent", "kind": "Gdef"}, "is_fully_escaped": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["s"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.raises.is_fully_escaped", "name": "is_fully_escaped", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["s"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_fully_escaped", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "overload": {".class": "SymbolTableNode", "cross_ref": "typing.overload", "kind": "Gdef"}, "possible_match": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["results", "used"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.raises.possible_match", "name": "possible_match", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["results", "used"], "arg_types": ["_pytest.raises.ResultHolder", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.int"], "extra_attrs": null, "type_ref": "builtins.set"}, {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "possible_match", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "raises": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "OverloadedFuncDef", "deprecated": null, "flags": [], "fullname": "_pytest.raises.raises", "impl": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [1, 2, 4], "arg_names": ["expected_exception", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload"], "fullname": "_pytest.raises.raises", "name": "raises", "type": {".class": "CallableType", "arg_kinds": [1, 2, 4], "arg_names": ["expected_exception", "args", "kwargs"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.E", "id": -1, "name": "E", "namespace": "_pytest.raises.raises", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.E", "id": -1, "name": "E", "namespace": "_pytest.raises.raises", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}], "extra_attrs": null, "type_ref": "builtins.tuple"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raises", "ret_type": {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, {".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.E", "id": -1, "name": "E", "namespace": "_pytest.raises.raises", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest._code.code.ExceptionInfo"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.E", "id": -1, "name": "E", "namespace": "_pytest.raises.raises", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}}, "items": [{".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 5, 5], "arg_names": ["expected_exception", "match", "check"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.raises.raises", "name": "raises", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["expected_exception", "match", "check"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.E", "id": -1, "name": "E", "namespace": "_pytest.raises.raises#0", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.E", "id": -1, "name": "E", "namespace": "_pytest.raises.raises#0", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.E", "id": -1, "name": "E", "namespace": "_pytest.raises.raises#0", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raises", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.E", "id": -1, "name": "E", "namespace": "_pytest.raises.raises#0", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.E", "id": -1, "name": "E", "namespace": "_pytest.raises.raises#0", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.raises.raises", "name": "raises", "type": {".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["expected_exception", "match", "check"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.E", "id": -1, "name": "E", "namespace": "_pytest.raises.raises#0", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.E", "id": -1, "name": "E", "namespace": "_pytest.raises.raises#0", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.E", "id": -1, "name": "E", "namespace": "_pytest.raises.raises#0", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raises", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.E", "id": -1, "name": "E", "namespace": "_pytest.raises.raises#0", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.E", "id": -1, "name": "E", "namespace": "_pytest.raises.raises#0", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 5], "arg_names": ["match", "check"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.raises.raises", "name": "raises", "type": {".class": "CallableType", "arg_kinds": [3, 5], "arg_names": ["match", "check"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.BaseException"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raises", "ret_type": {".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.raises.raises", "name": "raises", "type": {".class": "CallableType", "arg_kinds": [3, 5], "arg_names": ["match", "check"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.BaseException"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raises", "ret_type": {".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3], "arg_names": ["check"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.raises.raises", "name": "raises", "type": {".class": "CallableType", "arg_kinds": [3], "arg_names": ["check"], "arg_types": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.BaseException"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raises", "ret_type": {".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.raises.raises", "name": "raises", "type": {".class": "CallableType", "arg_kinds": [3], "arg_names": ["check"], "arg_types": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.BaseException"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raises", "ret_type": {".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["expected_exception", "func", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_overload", "is_decorated"], "fullname": "_pytest.raises.raises", "name": "raises", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["expected_exception", "func", "args", "kwargs"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.E", "id": -1, "name": "E", "namespace": "_pytest.raises.raises#3", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.E", "id": -1, "name": "E", "namespace": "_pytest.raises.raises#3", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raises", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.E", "id": -1, "name": "E", "namespace": "_pytest.raises.raises#3", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest._code.code.ExceptionInfo"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.E", "id": -1, "name": "E", "namespace": "_pytest.raises.raises#3", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}}, "is_overload": true, "var": {".class": "Var", "flags": ["is_ready", "is_inferred"], "fullname": "_pytest.raises.raises", "name": "raises", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["expected_exception", "func", "args", "kwargs"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.E", "id": -1, "name": "E", "namespace": "_pytest.raises.raises#3", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.E", "id": -1, "name": "E", "namespace": "_pytest.raises.raises#3", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raises", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.E", "id": -1, "name": "E", "namespace": "_pytest.raises.raises#3", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest._code.code.ExceptionInfo"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.E", "id": -1, "name": "E", "namespace": "_pytest.raises.raises#3", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}}}], "type": {".class": "Overloaded", "items": [{".class": "CallableType", "arg_kinds": [0, 5, 5], "arg_names": ["expected_exception", "match", "check"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.E", "id": -1, "name": "E", "namespace": "_pytest.raises.raises#0", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.E", "id": -1, "name": "E", "namespace": "_pytest.raises.raises#0", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.E", "id": -1, "name": "E", "namespace": "_pytest.raises.raises#0", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raises", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.E", "id": -1, "name": "E", "namespace": "_pytest.raises.raises#0", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.E", "id": -1, "name": "E", "namespace": "_pytest.raises.raises#0", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}, {".class": "CallableType", "arg_kinds": [3, 5], "arg_names": ["match", "check"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "<PERSON><PERSON>"}], "uses_pep604_syntax": true}, {".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.BaseException"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raises", "ret_type": {".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [3], "arg_names": ["check"], "arg_types": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": ["builtins.BaseException"], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raises", "ret_type": {".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "_pytest.raises.RaisesExc"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["expected_exception", "func", "args", "kwargs"], "arg_types": [{".class": "UnionType", "items": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.E", "id": -1, "name": "E", "namespace": "_pytest.raises.raises#3", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}, {".class": "Instance", "args": [{".class": "TypeType", "item": {".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.E", "id": -1, "name": "E", "namespace": "_pytest.raises.raises#3", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}}], "extra_attrs": null, "type_ref": "builtins.tuple"}], "uses_pep604_syntax": true}, {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "raises", "ret_type": {".class": "Instance", "args": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.E", "id": -1, "name": "E", "namespace": "_pytest.raises.raises#3", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "extra_attrs": null, "type_ref": "_pytest._code.code.ExceptionInfo"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": "builtins.BaseException", "fullname": "_pytest.raises.E", "id": -1, "name": "E", "namespace": "_pytest.raises.raises#3", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}]}}}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}, "repr_callable": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["fun"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.raises.repr_callable", "name": "repr_callable", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["fun"], "arg_types": [{".class": "CallableType", "arg_kinds": [0], "arg_names": [null], "arg_types": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.repr_callable", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "repr_callable", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": [{".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.raises.BaseExcT_1", "id": -1, "name": "BaseExcT_1", "namespace": "_pytest.raises.repr_callable", "upper_bound": "builtins.BaseException", "values": [], "variance": 0}]}}}, "stringify_exception": {".class": "SymbolTableNode", "cross_ref": "_pytest._code.code.stringify_exception", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef"}, "unescape": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["s"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.raises.unescape", "name": "unescape", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["s"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "unescape", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "warnings": {".class": "SymbolTableNode", "cross_ref": "warnings", "kind": "Gdef"}}, "path": "c:\\home-repos\\llm_proxy_server\\venv\\Lib\\site-packages\\_pytest\\raises.py"}