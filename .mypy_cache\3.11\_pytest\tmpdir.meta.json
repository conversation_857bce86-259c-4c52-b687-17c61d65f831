{"data_mtime": 1753376691, "dep_lines": [26, 6, 17, 22, 23, 27, 28, 30, 31, 32, 33, 4, 7, 8, 9, 10, 11, 12, 13, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 5, 10, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest.config.argparsing", "collections.abc", "_pytest.pathlib", "_pytest.compat", "_pytest.config", "_pytest.deprecated", "_pytest.fixtures", "_pytest.monkeypatch", "_pytest.nodes", "_pytest.reports", "_pytest.stash", "__future__", "dataclasses", "os", "pathlib", "re", "shutil", "tempfile", "typing", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "pluggy", "pluggy._hooks", "typing_extensions"], "hash": "3d723da5cd41cc99358ea3bdbcadede7f3c046cf", "id": "_pytest.tmpdir", "ignore_all": true, "interface_hash": "48cae9c1d2b436e66c52a810530f38820b09b232", "mtime": 1753376605, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\llm_proxy_server\\venv\\Lib\\site-packages\\_pytest\\tmpdir.py", "plugin_data": null, "size": 11263, "suppressed": [], "version_id": "1.15.0"}