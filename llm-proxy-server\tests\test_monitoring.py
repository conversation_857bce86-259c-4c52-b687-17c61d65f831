"""Tests for monitoring and metrics functionality"""

import pytest
import time
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta

from llm_proxy_server.monitoring import MetricsManager


@pytest.mark.unit
@pytest.mark.monitoring
class TestMetricsManager:
    """Test MetricsManager class"""
    
    def test_init(self):
        """Test MetricsManager initialization"""
        metrics = MetricsManager()
        
        assert metrics.start_time is not None
        assert len(metrics.request_count) == 0
        assert len(metrics.request_duration) == 0
        assert len(metrics.error_count) == 0
        assert len(metrics.active_connections) == 0
    
    def test_record_request_success(self, metrics_manager):
        """Test recording successful requests"""
        metrics_manager.record_request(
            endpoint="/api/chat",
            method="POST",
            status_code=200,
            duration=0.5,
            host="localhost:11434",
            model="llama2:latest"
        )
        
        # Check request count (key format is "METHOD endpoint")
        assert metrics_manager.request_count["POST /api/chat"] == 1

        # Check duration tracking
        assert len(metrics_manager.request_duration["POST /api/chat"]) == 1
        assert metrics_manager.request_duration["POST /api/chat"][0] == 0.5
        
        # Check status code tracking
        assert metrics_manager.status_codes[200] == 1
        
        # Check host stats
        assert metrics_manager.host_request_count["localhost:11434"] == 1
        
        # Check model stats
        assert metrics_manager.model_request_count["llama2:latest"] == 1
    
    def test_record_request_error(self, metrics_manager):
        """Test recording error requests"""
        metrics_manager.record_request(
            endpoint="/api/generate",
            method="POST", 
            status_code=500,
            duration=1.0,
            host="localhost:11435",
            model="mistral:latest",
            error="ConnectionError"
        )
        
        # Check error count
        assert metrics_manager.error_count["POST /api/generate"] == 1
        
        # Check host error tracking
        assert metrics_manager.host_error_count["localhost:11435"] == 1
        
        # Check model error tracking
        assert metrics_manager.model_error_count["mistral:latest"] == 1
        
        # Check status code tracking
        assert metrics_manager.status_codes[500] == 1
    
    def test_record_multiple_requests(self, metrics_manager):
        """Test recording multiple requests"""
        # Record multiple requests to same endpoint
        for i in range(5):
            metrics_manager.record_request(
                endpoint="/api/chat",
                method="POST",
                status_code=200,
                duration=0.1 * (i + 1),
                host="localhost:11434",
                model="llama2:latest"
            )
        
        assert metrics_manager.request_count["POST /api/chat"] == 5
        assert len(metrics_manager.request_duration["POST /api/chat"]) == 5
        assert metrics_manager.host_request_count["localhost:11434"] == 5
        assert metrics_manager.model_request_count["llama2:latest"] == 5
    
    def test_increment_decrement_active_connections(self, metrics_manager):
        """Test active connection tracking"""
        host = "localhost:11434"
        
        # Initially zero
        assert metrics_manager.active_connections[host] == 0
        
        # Increment
        metrics_manager.increment_active_connections(host)
        assert metrics_manager.active_connections[host] == 1
        
        metrics_manager.increment_active_connections(host)
        assert metrics_manager.active_connections[host] == 2
        
        # Decrement
        metrics_manager.decrement_active_connections(host)
        assert metrics_manager.active_connections[host] == 1
        
        metrics_manager.decrement_active_connections(host)
        assert metrics_manager.active_connections[host] == 0
    
    def test_decrement_connections_below_zero(self, metrics_manager):
        """Test that active connections don't go below zero"""
        host = "localhost:11434"
        
        # Should not go below 0
        metrics_manager.decrement_active_connections(host)
        assert metrics_manager.active_connections[host] == 0
    
    def test_record_health_check(self, metrics_manager):
        """Test health check recording"""
        host = "localhost:11434"
        
        # Record healthy check
        metrics_manager.record_health_check(host, True, 0.1)
        assert host in metrics_manager.health_checks
        assert len(metrics_manager.health_checks[host]) == 1
        assert metrics_manager.health_checks[host][0]["healthy"] is True
        assert metrics_manager.health_checks[host][0]["response_time"] == 0.1

        # Record unhealthy check
        metrics_manager.record_health_check(host, False, 5.0)
        assert len(metrics_manager.health_checks[host]) == 2
        assert metrics_manager.health_checks[host][1]["healthy"] is False
        assert metrics_manager.health_checks[host][1]["response_time"] == 5.0
    
    def test_get_request_stats(self, metrics_manager):
        """Test getting request statistics"""
        # Record some requests
        metrics_manager.record_request("/api/chat", "POST", 200, 0.5)
        metrics_manager.record_request("/api/chat", "POST", 200, 0.3)
        metrics_manager.record_request("/api/generate", "POST", 500, 1.0)
        
        stats = metrics_manager.get_request_stats()
        
        assert "total_requests" in stats
        assert "total_errors" in stats
        assert "error_rate" in stats
        assert "requests_by_endpoint" in stats
        assert "avg_response_times" in stats
        
        assert stats["total_requests"] == 3
        assert stats["total_errors"] == 1
        assert stats["error_rate"] == 1/3
        
        # Check endpoint breakdown (keys are "METHOD endpoint")
        assert stats["requests_by_endpoint"]["POST /api/chat"] == 2
        assert stats["requests_by_endpoint"]["POST /api/generate"] == 1
    
    def test_get_host_stats(self, metrics_manager):
        """Test getting host statistics"""
        # Record requests for different hosts
        metrics_manager.record_request("/api/chat", "POST", 200, 0.5, host="host1")
        metrics_manager.record_request("/api/chat", "POST", 500, 1.0, host="host1")
        metrics_manager.record_request("/api/generate", "POST", 200, 0.3, host="host2")
        
        metrics_manager.increment_active_connections("host1")
        metrics_manager.record_health_check("host1", True, 0.1)
        metrics_manager.record_health_check("host2", False, 2.0)
        
        stats = metrics_manager.get_host_stats()
        
        assert "host1" in stats
        assert "host2" in stats
        
        # Check host1 stats (using actual field names)
        host1_stats = stats["host1"]
        assert host1_stats["requests"] == 2
        assert host1_stats["errors"] == 1
        assert host1_stats["is_healthy"] is True

        # Check host2 stats
        host2_stats = stats["host2"]
        assert host2_stats["requests"] == 1
        assert host2_stats["errors"] == 0
        assert host2_stats["is_healthy"] is False
    
    def test_get_model_stats(self, metrics_manager):
        """Test getting model statistics"""
        # Record requests for different models
        metrics_manager.record_request("/api/chat", "POST", 200, 0.5, model="llama2")
        metrics_manager.record_request("/api/chat", "POST", 200, 0.3, model="llama2")
        metrics_manager.record_request("/api/generate", "POST", 500, 1.0, model="mistral")
        
        stats = metrics_manager.get_model_stats()
        
        assert "llama2" in stats
        assert "mistral" in stats
        
        # Check llama2 stats (using actual field names)
        llama2_stats = stats["llama2"]
        assert llama2_stats["requests"] == 2
        assert llama2_stats["errors"] == 0
        assert llama2_stats["error_rate"] == 0.0

        # Check mistral stats
        mistral_stats = stats["mistral"]
        assert mistral_stats["requests"] == 1
        assert mistral_stats["errors"] == 1
    
    def test_get_health_status(self, metrics_manager):
        """Test getting overall health status"""
        # Record some activity
        metrics_manager.record_request("/api/chat", "POST", 200, 0.5)
        metrics_manager.record_request("/api/generate", "POST", 500, 1.0)
        
        health = metrics_manager.get_health_status()
        
        assert "status" in health
        assert "uptime" in health
        assert "total_requests" in health
        assert "total_errors" in health
        assert "error_rate" in health
        
        assert health["total_requests"] == 2
        assert health["total_errors"] == 1
        assert health["error_rate"] == 0.5
        assert isinstance(health["uptime"], float)
        assert health["uptime"] >= 0  # Allow zero for very fast tests
    
    def test_get_recent_activity(self, metrics_manager):
        """Test getting recent activity"""
        # Record some requests
        metrics_manager.record_request("/api/chat", "POST", 200, 0.5, host="host1", model="llama2")
        metrics_manager.record_request("/api/generate", "POST", 500, 1.0, host="host2", model="mistral")
        
        activity = metrics_manager.get_recent_activity()
        
        assert isinstance(activity, list)
        assert len(activity) == 2
        
        # Check activity entries
        for entry in activity:
            assert "timestamp" in entry
            assert "endpoint" in entry
            assert "method" in entry
            assert "status_code" in entry
            assert "duration" in entry
    
    def test_generate_metrics(self, metrics_manager):
        """Test comprehensive metrics generation"""
        # Record various activities
        metrics_manager.record_request("/api/chat", "POST", 200, 0.5, host="host1", model="llama2")
        metrics_manager.record_health_check("host1", True, 0.1)
        metrics_manager.increment_active_connections("host1")
        
        metrics = metrics_manager.generate_metrics()
        
        assert "request_stats" in metrics
        assert "host_stats" in metrics
        assert "model_stats" in metrics
        assert "health_status" in metrics
        assert "recent_activity" in metrics
        
        # Verify structure
        assert isinstance(metrics["request_stats"], dict)
        assert isinstance(metrics["host_stats"], dict)
        assert isinstance(metrics["model_stats"], dict)
        assert isinstance(metrics["health_status"], dict)
        assert isinstance(metrics["recent_activity"], list)
    
    def test_reset_metrics(self, metrics_manager):
        """Test resetting all metrics"""
        # Record some data
        metrics_manager.record_request("/api/chat", "POST", 200, 0.5, host="host1", model="llama2")
        metrics_manager.increment_active_connections("host1")
        metrics_manager.record_health_check("host1", True, 0.1)
        
        # Verify data exists
        assert len(metrics_manager.request_count) > 0
        assert len(metrics_manager.active_connections) > 0
        assert len(metrics_manager.health_checks) > 0
        
        # Reset
        metrics_manager.reset_metrics()
        
        # Verify data is cleared
        assert len(metrics_manager.request_count) == 0
        assert len(metrics_manager.request_duration) == 0
        assert len(metrics_manager.error_count) == 0
        assert len(metrics_manager.recent_requests) == 0
        assert len(metrics_manager.health_checks) == 0
    
    def test_metrics_thread_safety(self, metrics_manager):
        """Test metrics collection thread safety"""
        import threading
        import time
        
        def record_requests():
            for i in range(10):
                metrics_manager.record_request(
                    f"/api/test{i % 3}",
                    "POST",
                    200,
                    0.1,
                    host=f"host{i % 2}",
                    model=f"model{i % 2}"
                )
                time.sleep(0.001)  # Small delay
        
        # Start multiple threads
        threads = []
        for _ in range(3):
            thread = threading.Thread(target=record_requests)
            threads.append(thread)
            thread.start()
        
        # Wait for completion
        for thread in threads:
            thread.join()
        
        # Verify total requests
        stats = metrics_manager.get_request_stats()
        assert stats["total_requests"] == 30  # 3 threads * 10 requests each
