{".class": "MypyFile", "_fullname": "tests.test_monitoring", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "MagicMock": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.MagicMock", "kind": "Gdef"}, "MetricsManager": {".class": "SymbolTableNode", "cross_ref": "llm_proxy_server.monitoring.MetricsManager", "kind": "Gdef"}, "TestMetricsManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_monitoring.TestMetricsManager", "name": "TestMetricsManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_monitoring.TestMetricsManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_monitoring", "mro": ["tests.test_monitoring.TestMetricsManager", "builtins.object"], "names": {".class": "SymbolTable", "test_decrement_connections_below_zero": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "metrics_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_monitoring.TestMetricsManager.test_decrement_connections_below_zero", "name": "test_decrement_connections_below_zero", "type": null}}, "test_generate_metrics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "metrics_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_monitoring.TestMetricsManager.test_generate_metrics", "name": "test_generate_metrics", "type": null}}, "test_get_health_status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "metrics_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_monitoring.TestMetricsManager.test_get_health_status", "name": "test_get_health_status", "type": null}}, "test_get_host_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "metrics_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_monitoring.TestMetricsManager.test_get_host_stats", "name": "test_get_host_stats", "type": null}}, "test_get_model_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "metrics_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_monitoring.TestMetricsManager.test_get_model_stats", "name": "test_get_model_stats", "type": null}}, "test_get_recent_activity": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "metrics_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_monitoring.TestMetricsManager.test_get_recent_activity", "name": "test_get_recent_activity", "type": null}}, "test_get_request_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "metrics_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_monitoring.TestMetricsManager.test_get_request_stats", "name": "test_get_request_stats", "type": null}}, "test_increment_decrement_active_connections": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "metrics_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_monitoring.TestMetricsManager.test_increment_decrement_active_connections", "name": "test_increment_decrement_active_connections", "type": null}}, "test_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_monitoring.TestMetricsManager.test_init", "name": "test_init", "type": null}}, "test_metrics_thread_safety": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "metrics_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_monitoring.TestMetricsManager.test_metrics_thread_safety", "name": "test_metrics_thread_safety", "type": null}}, "test_record_health_check": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "metrics_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_monitoring.TestMetricsManager.test_record_health_check", "name": "test_record_health_check", "type": null}}, "test_record_multiple_requests": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "metrics_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_monitoring.TestMetricsManager.test_record_multiple_requests", "name": "test_record_multiple_requests", "type": null}}, "test_record_request_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "metrics_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_monitoring.TestMetricsManager.test_record_request_error", "name": "test_record_request_error", "type": null}}, "test_record_request_success": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "metrics_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_monitoring.TestMetricsManager.test_record_request_success", "name": "test_record_request_success", "type": null}}, "test_reset_metrics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "metrics_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_monitoring.TestMetricsManager.test_reset_metrics", "name": "test_reset_metrics", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_monitoring.TestMetricsManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_monitoring.TestMetricsManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_monitoring.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_monitoring.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_monitoring.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_monitoring.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_monitoring.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_monitoring.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "patch": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.patch", "kind": "Gdef"}, "pytest": {".class": "SymbolTableNode", "cross_ref": "pytest", "kind": "Gdef"}, "time": {".class": "SymbolTableNode", "cross_ref": "time", "kind": "Gdef"}, "timedelta": {".class": "SymbolTableNode", "cross_ref": "datetime.<PERSON><PERSON><PERSON>", "kind": "Gdef"}}, "path": "C:\\home-repos\\llm_proxy_server\\llm-proxy-server\\tests\\test_monitoring.py"}