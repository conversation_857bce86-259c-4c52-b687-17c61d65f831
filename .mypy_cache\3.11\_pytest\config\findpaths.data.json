{".class": "MypyFile", "_fullname": "_pytest.config.findpaths", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "CFG_PYTEST_SECTION": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready", "is_inferred", "has_explicit_value"], "fullname": "_pytest.config.findpaths.CFG_PYTEST_SECTION", "name": "CFG_PYTEST_SECTION", "type": "builtins.str"}}, "ConfigDict": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeAlias", "alias_tvars": [], "column": 4, "fullname": "_pytest.config.findpaths.ConfigDict", "line": 26, "no_args": false, "normalized": false, "python_3_12_type_alias": false, "target": {".class": "Instance", "args": ["builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "uses_pep604_syntax": false}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "Iterable": {".class": "SymbolTableNode", "cross_ref": "typing.Iterable", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "Sequence": {".class": "SymbolTableNode", "cross_ref": "typing.Sequence", "kind": "Gdef"}, "TYPE_CHECKING": {".class": "SymbolTableNode", "cross_ref": "typing.TYPE_CHECKING", "kind": "Gdef"}, "TypeAlias": {".class": "SymbolTableNode", "cross_ref": "typing.TypeAlias", "kind": "Gdef"}, "Union": {".class": "SymbolTableNode", "cross_ref": "typing.Union", "kind": "Gdef"}, "UsageError": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.exceptions.UsageError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.config.findpaths.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.config.findpaths.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.config.findpaths.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.config.findpaths.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.config.findpaths.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.config.findpaths.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_parse_ini_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["path"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.config.findpaths._parse_ini_config", "name": "_parse_ini_config", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["path"], "arg_types": ["pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_parse_ini_config", "ret_type": "iniconfig.IniConfig", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "absolutepath": {".class": "SymbolTableNode", "cross_ref": "_pytest.pathlib.absolutepath", "kind": "Gdef"}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "commonpath": {".class": "SymbolTableNode", "cross_ref": "_pytest.pathlib.commonpath", "kind": "Gdef"}, "determine_setup": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [3, 3, 3, 3], "arg_names": ["inifile", "args", "rootdir_cmd_arg", "invocation_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.config.findpaths.determine_setup", "name": "determine_setup", "type": {".class": "CallableType", "arg_kinds": [3, 3, 3, 3], "arg_names": ["inifile", "args", "rootdir_cmd_arg", "invocation_dir"], "arg_types": [{".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Sequence"}, {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": true}, "pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "determine_setup", "ret_type": {".class": "TupleType", "implicit": false, "items": ["pathlib.Path", {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "_pytest.config.findpaths.ConfigDict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "fail": {".class": "SymbolTableNode", "cross_ref": "_pytest.outcomes.fail", "kind": "Gdef"}, "get_common_ancestor": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["invocation_dir", "paths"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.config.findpaths.get_common_ancestor", "name": "get_common_ancestor", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["invocation_dir", "paths"], "arg_types": ["pathlib.Path", {".class": "Instance", "args": ["pathlib.Path"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_common_ancestor", "ret_type": "pathlib.Path", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_dirs_from_args": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.config.findpaths.get_dirs_from_args", "name": "get_dirs_from_args", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["args"], "arg_types": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_dirs_from_args", "ret_type": {".class": "Instance", "args": ["pathlib.Path"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "iniconfig": {".class": "SymbolTableNode", "cross_ref": "iniconfig", "kind": "Gdef"}, "is_fs_root": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["p"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.config.findpaths.is_fs_root", "name": "is_fs_root", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["p"], "arg_types": ["pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "is_fs_root", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "load_config_dict_from_file": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["filepath"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.config.findpaths.load_config_dict_from_file", "name": "load_config_dict_from_file", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["filepath"], "arg_types": ["pathlib.Path"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "load_config_dict_from_file", "ret_type": {".class": "UnionType", "items": [{".class": "TypeAliasType", "args": [], "type_ref": "_pytest.config.findpaths.ConfigDict"}, {".class": "NoneType"}], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "locate_config": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["invocation_dir", "args"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.config.findpaths.locate_config", "name": "locate_config", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["invocation_dir", "args"], "arg_types": ["pathlib.Path", {".class": "Instance", "args": ["pathlib.Path"], "extra_attrs": null, "type_ref": "typing.Iterable"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "locate_config", "ret_type": {".class": "TupleType", "implicit": false, "items": [{".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["pathlib.Path", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "TypeAliasType", "args": [], "type_ref": "_pytest.config.findpaths.ConfigDict"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "safe_exists": {".class": "SymbolTableNode", "cross_ref": "_pytest.pathlib.safe_exists", "kind": "Gdef"}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}}, "path": "c:\\home-repos\\llm_proxy_server\\venv\\Lib\\site-packages\\_pytest\\config\\findpaths.py"}