# PowerShell script to start the complete local LLM stack
# This includes the LLM Proxy Server + Open WebUI for testing

Write-Host "🚀 Starting Local LLM Stack (Proxy + WebUI)" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

# Function to check if Dock<PERSON> is running
function Test-DockerRunning {
    try {
        docker version | Out-Null
        return $true
    }
    catch {
        return $false
    }
}

# Function to stop existing containers if they exist
function Stop-ExistingContainers {
    Write-Host "`n🛑 Stopping any existing containers..." -ForegroundColor Yellow
    
    # Stop standalone Open WebUI if running
    $existingOpenWebUI = docker ps -q --filter "name=open-webui"
    if ($existingOpenWebUI) {
        Write-Host "Stopping existing Open WebUI container..." -ForegroundColor Gray
        docker stop $existingOpenWebUI | Out-Null
        docker rm $existingOpenWebUI | Out-Null
    }
    
    # Stop any existing proxy containers
    docker-compose -f docker-compose.local.yml down 2>$null
}

# Function to start the stack
function Start-Stack {
    Write-Host "`n🔧 Building and starting the stack..." -ForegroundColor Yellow
    
    try {
        # Build and start all services
        docker-compose -f docker-compose.local.yml up --build -d
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Stack started successfully!" -ForegroundColor Green
            return $true
        } else {
            Write-Host "❌ Failed to start stack" -ForegroundColor Red
            return $false
        }
    }
    catch {
        Write-Host "❌ Error starting stack: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to wait for services to be ready
function Wait-ForServices {
    Write-Host "`n⏳ Waiting for services to be ready..." -ForegroundColor Yellow
    
    $maxWait = 120  # 2 minutes
    $waited = 0
    $interval = 5
    
    while ($waited -lt $maxWait) {
        # Check proxy health
        try {
            $proxyResponse = Invoke-RestMethod -Uri "http://localhost:11440/proxy/health" -TimeoutSec 5 -ErrorAction Stop
            $proxyReady = $true
        }
        catch {
            $proxyReady = $false
        }
        
        # Check WebUI health
        try {
            $webuiResponse = Invoke-RestMethod -Uri "http://localhost:3000/health" -TimeoutSec 5 -ErrorAction Stop
            $webuiReady = $true
        }
        catch {
            $webuiReady = $false
        }
        
        if ($proxyReady -and $webuiReady) {
            Write-Host "✅ All services are ready!" -ForegroundColor Green
            return $true
        }
        
        Write-Host "⏳ Services starting... ($waited/$maxWait seconds)" -ForegroundColor Gray
        Start-Sleep -Seconds $interval
        $waited += $interval
    }
    
    Write-Host "⚠️  Services may still be starting. Check logs if needed." -ForegroundColor Yellow
    return $false
}

# Function to show service status
function Show-ServiceStatus {
    Write-Host "`n📊 Service Status:" -ForegroundColor Cyan
    Write-Host "==================" -ForegroundColor Cyan
    
    # Show container status
    docker-compose -f docker-compose.local.yml ps
    
    Write-Host "`n🌐 Access URLs:" -ForegroundColor Cyan
    Write-Host "===============" -ForegroundColor Cyan
    Write-Host "• Open WebUI:      http://localhost:3000" -ForegroundColor White
    Write-Host "• Proxy Health:    http://localhost:11440/proxy/health" -ForegroundColor White
    Write-Host "• Proxy Metrics:   http://localhost:11440/proxy/metrics" -ForegroundColor White
    Write-Host "• Proxy API:       http://localhost:11440/api/tags" -ForegroundColor White
    
    Write-Host "`n📋 Quick Commands:" -ForegroundColor Cyan
    Write-Host "==================" -ForegroundColor Cyan
    Write-Host "• View logs:       docker-compose -f docker-compose.local.yml logs -f" -ForegroundColor Gray
    Write-Host "• Stop stack:      docker-compose -f docker-compose.local.yml down" -ForegroundColor Gray
    Write-Host "• Restart:         docker-compose -f docker-compose.local.yml restart" -ForegroundColor Gray
}

# Main execution
try {
    # Check Docker
    if (-not (Test-DockerRunning)) {
        Write-Host "❌ Docker is not running. Please start Docker Desktop first." -ForegroundColor Red
        exit 1
    }
    
    # Stop existing containers
    Stop-ExistingContainers
    
    # Start the stack
    if (-not (Start-Stack)) {
        Write-Host "`n❌ Failed to start the stack. Check the logs:" -ForegroundColor Red
        Write-Host "docker-compose -f docker-compose.local.yml logs" -ForegroundColor Gray
        exit 1
    }
    
    # Wait for services
    Wait-ForServices
    
    # Show status
    Show-ServiceStatus
    
    Write-Host "`n🎉 Local LLM Stack is ready!" -ForegroundColor Green
    Write-Host "Open your browser to http://localhost:3000 to start using Open WebUI" -ForegroundColor White
    Write-Host "The WebUI will connect to your LLM Proxy Server which load balances across your Ollama instances." -ForegroundColor Gray
    
}
catch {
    Write-Host "`n❌ An error occurred: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Check Docker Desktop and try again." -ForegroundColor Gray
    exit 1
}
