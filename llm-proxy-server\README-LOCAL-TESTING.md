# Local Testing Setup for LLM Proxy Server + Open WebUI

This guide helps you test the complete LLM stack locally on Windows with Docker Desktop before deploying to production. The setup includes:
- **LLM Proxy Server** - Load balances requests across your Ollama instances
- **Open WebUI** - Web interface for interacting with LLMs through the proxy

## Prerequisites

1. **Docker Desktop** installed and running on Windows
2. **PowerShell** (comes with Windows)
3. At least 4GB of available RAM for the containers

## Quick Start

### 1. Start the Complete Stack (Recommended)

```powershell
# Navigate to the project directory
cd C:\home-repos\llm_proxy_server\llm-proxy-server

# Use the automated startup script
.\start-local-stack.ps1
```

This will:
- Stop any existing containers
- Build and start the LLM Proxy Server
- Start Open WebUI connected to the proxy
- Wait for all services to be ready
- Show you the access URLs

### 2. Alternative: Manual Start

```powershell
# Start all services manually
docker-compose -f docker-compose.local.yml up --build -d

# Check if containers are running
docker-compose -f docker-compose.local.yml ps
```

### 2. Wait for Services to Initialize

```powershell
# Watch the logs to see when everything is ready
docker-compose -f docker-compose.local.yml logs -f

# Or check individual service logs
docker-compose -f docker-compose.local.yml logs llm-proxy-server
```

### 3. Run the Test Script

```powershell
# Run the automated test script
.\test-local-setup.ps1
```

## What's Running

| Service | Container Name | Port | Purpose |
|---------|---------------|------|---------|
| **Open WebUI** | `open-webui-local` | **3000** | **Web interface for LLM interaction** |
| **LLM Proxy Server** | `llm-proxy-server-local` | **11440** | **Load balancer for Ollama instances** |

### External Ollama Services (Your Real Servers)
| Server | IP Address | Port | Purpose |
|--------|------------|------|---------|
| lynn-pc | ************ | 11434 | Ollama instance 1 |
| home-game-server | ************ | 11434 | Ollama instance 2 |
| t5810 | ************ | 11434 | Ollama instance 3 |

## Using Open WebUI

### 1. Access the Web Interface
1. **Open your browser** to http://localhost:3000
2. **Create an account** (first user becomes admin)
3. **Start chatting** with your LLMs through the proxy

### 2. Features Available
- **Chat Interface**: Interactive chat with your LLMs
- **Model Selection**: Choose from models available on your Ollama servers
- **Load Balancing**: Requests automatically distributed across your servers
- **Real-time Responses**: Streaming responses from your LLMs
- **Chat History**: Persistent conversation history
- **Model Management**: View available models and their details

### 3. Configuration
The Open WebUI is pre-configured to:
- Connect to your LLM Proxy Server at `http://llm-proxy-server:11440`
- Use your real Ollama servers (lynn-pc, home-game-server, t5810)
- Load balance requests automatically
- Provide debug information when needed

## Testing Scenarios

### 1. Basic Health Check
```powershell
curl http://localhost:11434/proxy/health
```

### 2. Test Load Balancing
```powershell
# Install a model on one of the Ollama instances
docker exec ollama-main ollama pull llama2

# Test API through proxy
curl -X POST http://localhost:11434/api/chat `
  -H "Content-Type: application/json" `
  -d '{"model":"llama2","messages":[{"role":"user","content":"Hello!"}]}'
```

### 3. Test Authentication (if enabled)
```powershell
# With auth disabled (default), this should work without credentials
curl http://localhost:11434/api/tags

# To test with auth enabled, modify docker-compose.local.yml:
# Change AUTH_ENABLED=false to AUTH_ENABLED=true
# Then restart: docker-compose -f docker-compose.local.yml restart llm-proxy-server
```

### 4. Test Metrics
```powershell
curl http://localhost:11434/proxy/metrics
```

## Configuration Differences from Production

| Setting | Local | Production |
|---------|-------|------------|
| Host Config | `hosts.local.json` | `hosts.json` |
| Debug Mode | `true` | `false` |
| Log Level | `DEBUG` | `INFO` |
| Auth Enabled | `false` | `false` (now default) |
| Container Names | Docker service names | Real server hostnames |

## Troubleshooting

### Containers Won't Start
```powershell
# Check Docker Desktop is running
docker version

# Check for port conflicts
netstat -an | findstr :11434

# View detailed logs
docker-compose -f docker-compose.local.yml logs
```

### Proxy Can't Connect to Ollama
```powershell
# Check if Ollama containers are healthy
docker-compose -f docker-compose.local.yml ps

# Test direct connection to Ollama
curl http://localhost:11435/api/tags

# Check network connectivity
docker exec llm-proxy-server-local ping ollama-main
```

### Memory Issues
```powershell
# Check Docker resource usage
docker stats

# Reduce number of Ollama instances if needed
# Comment out ollama-gpu2 in docker-compose.local.yml
```

## Cleanup

```powershell
# Stop all services
docker-compose -f docker-compose.local.yml down

# Remove volumes (deletes downloaded models)
docker-compose -f docker-compose.local.yml down -v

# Remove images (frees more space)
docker-compose -f docker-compose.local.yml down --rmi all -v
```

## Next Steps

Once local testing is successful:

1. ✅ Verify all endpoints work
2. ✅ Test load balancing behavior
3. ✅ Check logs for errors
4. ✅ Test with/without authentication
5. 🚀 Deploy to production with confidence!

## Production Deployment

When ready to deploy to production:

```powershell
# Use the production docker-compose.yml
docker-compose -f docker-compose.yml up -d
```

The main differences will be:
- Real Ollama server hostnames instead of container names
- Production-grade logging and monitoring
- Proper authentication configuration
- External network configuration
