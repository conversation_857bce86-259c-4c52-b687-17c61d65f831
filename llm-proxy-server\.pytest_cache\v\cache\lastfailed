{"tests/test_models.py::TestHostConfig::test_invalid_weight": true, "tests/test_models.py::TestHostConfig::test_invalid_max_connections": true, "tests/test_models.py::TestChatMessage::test_invalid_role": true, "tests/test_models.py::TestChatRequest::test_empty_messages": true, "tests/test_load_balancer.py::TestLoadBalancer::test_add_host": true, "tests/test_load_balancer.py::TestLoadBalancer::test_add_multiple_hosts": true, "tests/test_load_balancer.py::TestLoadBalancer::test_remove_host": true, "tests/test_load_balancer.py::TestLoadBalancer::test_remove_nonexistent_host": true, "tests/test_load_balancer.py::TestLoadBalancer::test_set_weight_nonexistent_host": true, "tests/test_load_balancer.py::TestLoadBalancer::test_enable_disable_host": true, "tests/test_load_balancer.py::TestLoadBalancer::test_get_next_host_round_robin": true, "tests/test_load_balancer.py::TestLoadBalancer::test_get_next_host_least_connections": true, "tests/test_load_balancer.py::TestLoadBalancer::test_get_next_host_weighted_random": true, "tests/test_load_balancer.py::TestLoadBalancer::test_get_next_host_no_hosts": true, "tests/test_load_balancer.py::TestLoadBalancer::test_get_next_host_all_disabled": true, "tests/test_load_balancer.py::TestLoadBalancer::test_reset_connections": true}