# PowerShell script to test LLM Proxy health and basic functionality

Write-Host "🏥 LLM Proxy Health Check" -ForegroundColor Green
Write-Host "=========================" -ForegroundColor Green

# Function to map IP addresses to server names
function Get-ServerFromHost {
    param([string]$HostIP)
    
    if ($HostIP -match "http://([0-9.]+):") {
        $ip = $matches[1]
    } else {
        $ip = $HostIP
    }
    
    switch ($ip) {
        "************" { return "lynn-pc" }
        "************" { return "home-game-server" }
        "************" { return "t5810" }
        default { return "unknown ($ip)" }
    }
}

try {
    # 1. Health Check
    Write-Host "`n🔍 Checking proxy health..." -ForegroundColor Yellow
    try {
        $health = Invoke-RestMethod -Uri "http://localhost:11440/proxy/health" -TimeoutSec 5
        Write-Host "✅ Proxy is healthy" -ForegroundColor Green
        Write-Host "Version: $($health.version)" -ForegroundColor Gray
        Write-Host "Status: $($health.status)" -ForegroundColor Gray
    }
    catch {
        Write-Host "❌ Proxy health check failed: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
    
    # 2. Status Check
    Write-Host "`n📊 Checking proxy status..." -ForegroundColor Yellow
    try {
        $status = Invoke-RestMethod -Uri "http://localhost:11440/proxy/status" -TimeoutSec 5
        Write-Host "✅ Proxy status retrieved" -ForegroundColor Green
        Write-Host "Total hosts: $($status.total_hosts)" -ForegroundColor Gray
        Write-Host "Active hosts: $($status.active_hosts)" -ForegroundColor Gray
        Write-Host "Total requests: $($status.total_requests)" -ForegroundColor Gray
    }
    catch {
        Write-Host "❌ Status check failed: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # 3. Models Check
    Write-Host "`n📋 Checking available models..." -ForegroundColor Yellow
    try {
        $models = Invoke-RestMethod -Uri "http://localhost:11440/api/tags" -TimeoutSec 10
        Write-Host "✅ Found $($models.models.Count) models" -ForegroundColor Green
        Write-Host "Available models:" -ForegroundColor Gray
        $models.models | Select-Object -First 5 | ForEach-Object { 
            Write-Host "  • $($_.name)" -ForegroundColor White 
        }
        if ($models.models.Count -gt 5) {
            Write-Host "  ... and $($models.models.Count - 5) more" -ForegroundColor Gray
        }
    }
    catch {
        Write-Host "❌ Models check failed: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # 4. Quick Chat Test
    Write-Host "`n💬 Testing chat functionality..." -ForegroundColor Yellow
    try {
        $testModel = "llama3:latest"
        $body = @{
            model = $testModel
            messages = @(
                @{
                    role = "user"
                    content = "Health check test. Respond with just 'OK'."
                }
            )
            stream = $false
        } | ConvertTo-Json -Depth 3
        
        $startTime = Get-Date
        $response = Invoke-RestMethod -Uri "http://localhost:11440/api/chat" -Method POST -Body $body -ContentType "application/json" -TimeoutSec 15
        $endTime = Get-Date
        $duration = ($endTime - $startTime).TotalSeconds
        
        if ($response -and $response.message) {
            Write-Host "✅ Chat test successful" -ForegroundColor Green
            Write-Host "Response time: $([Math]::Round($duration, 2))s" -ForegroundColor Gray
            Write-Host "Response: $($response.message.content)" -ForegroundColor Gray
            
            if ($response.proxy_server) {
                $serverName = Get-ServerFromHost -HostIP $response.proxy_server
                Write-Host "Served by: $serverName" -ForegroundColor Cyan
            }
        }
    }
    catch {
        Write-Host "❌ Chat test failed: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    # 5. Metrics Check (if available)
    Write-Host "`n📈 Checking metrics..." -ForegroundColor Yellow
    try {
        $metrics = Invoke-RestMethod -Uri "http://localhost:11440/proxy/metrics" -TimeoutSec 5
        Write-Host "✅ Metrics retrieved successfully" -ForegroundColor Green
        
        if ($metrics.request_stats) {
            Write-Host "Total requests: $($metrics.request_stats.total_requests)" -ForegroundColor Gray
            Write-Host "Total errors: $($metrics.request_stats.total_errors)" -ForegroundColor Gray
        }
    }
    catch {
        Write-Host "⚠️  Metrics not available (may require admin access)" -ForegroundColor Yellow
    }
    
    Write-Host "`n🎉 Health check complete!" -ForegroundColor Green
    Write-Host "🌐 Open WebUI: http://localhost:3000" -ForegroundColor Cyan
    
}
catch {
    Write-Host "`n❌ Health check failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Make sure the proxy is running: docker-compose -f docker-compose.local.yml up -d" -ForegroundColor Gray
}
