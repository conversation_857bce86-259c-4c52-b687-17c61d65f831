# PowerShell script for model management operations

Write-Host "🔧 Model Management Test" -ForegroundColor Green
Write-Host "========================" -ForegroundColor Green

# Load System.Web for URL encoding
Add-Type -AssemblyName System.Web

# Function to map IP addresses to server names
function Get-ServerFromHost {
    param([string]$HostIP)
    
    if ($HostIP -match "http://([0-9.]+):") {
        $ip = $matches[1]
    } else {
        $ip = $HostIP
    }
    
    switch ($ip) {
        "************" { return "lynn-pc" }
        "************" { return "home-game-server" }
        "************" { return "t5810" }
        default { return "unknown ($ip)" }
    }
}

# Function to check model status
function Get-ModelStatus {
    param([string]$ModelName)
    
    Write-Host "`n🔍 Checking $ModelName status..." -ForegroundColor Yellow
    try {
        $encodedModel = [System.Web.HttpUtility]::UrlEncode($ModelName)
        $response = Invoke-RestMethod -Uri "http://localhost:11440/api/model/$encodedModel/status" -Method GET -TimeoutSec 10
        
        Write-Host "📊 Status: $($response.servers_with_model)/$($response.total_servers) servers" -ForegroundColor Cyan
        
        foreach ($server in $response.servers.PSObject.Properties) {
            $serverName = Get-ServerFromHost -HostIP $server.Name
            $hasModel = $server.Value
            $status = if ($hasModel) { "✅ Available" } else { "❌ Missing" }
            $color = if ($hasModel) { "Green" } else { "Red" }
            Write-Host "  • $serverName`: $status" -ForegroundColor $color
        }
        
        return $response
    }
    catch {
        Write-Host "❌ Failed to check status: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

# Function to install model on all servers
function Install-ModelAllServers {
    param([string]$ModelName)
    
    Write-Host "`n📥 Installing $ModelName on all servers..." -ForegroundColor Green
    Write-Host "⚠️  This may take several minutes" -ForegroundColor Yellow
    
    try {
        $body = @{
            name = $ModelName
        } | ConvertTo-Json -Depth 3
        
        $startTime = Get-Date
        $response = Invoke-RestMethod -Uri "http://localhost:11440/api/pull" -Method POST -Body $body -ContentType "application/json" -TimeoutSec 600
        $duration = ((Get-Date) - $startTime).TotalMinutes
        
        Write-Host "`n📊 Installation Results:" -ForegroundColor Cyan
        Write-Host "Status: $($response.status)" -ForegroundColor $(if ($response.status -eq "success") { "Green" } else { "Red" })
        Write-Host "Duration: $([Math]::Round($duration, 1)) minutes" -ForegroundColor Gray
        
        foreach ($server in $response.results.PSObject.Properties) {
            $serverName = Get-ServerFromHost -HostIP $server.Name
            $result = $server.Value
            $color = if ($result.status -eq "success") { "Green" } else { "Red" }
            Write-Host "  • $serverName`: $($result.status)" -ForegroundColor $color
        }
        
        return $response.status -eq "success"
    }
    catch {
        Write-Host "❌ Installation failed: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to show model distribution analysis
function Show-ModelDistribution {
    Write-Host "`n📊 Model Distribution Analysis" -ForegroundColor Yellow
    
    # Get models from proxy
    try {
        $proxyModels = Invoke-RestMethod -Uri "http://localhost:11440/api/tags" -Method GET -TimeoutSec 10
        Write-Host "✅ Proxy reports $($proxyModels.models.Count) models" -ForegroundColor Green
        
        # Check a few key models
        $keyModels = @("llama3:latest", "tinyllama:latest", "nomic-embed-text:latest")
        
        foreach ($model in $keyModels) {
            if ($proxyModels.models.name -contains $model) {
                Get-ModelStatus -ModelName $model
            }
        }
    }
    catch {
        Write-Host "❌ Failed to get model list: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Main execution
try {
    # Check proxy health
    Write-Host "`n🔍 Checking proxy..." -ForegroundColor Yellow
    try {
        $null = Invoke-RestMethod -Uri "http://localhost:11440/proxy/health" -TimeoutSec 5
        Write-Host "✅ Proxy is running" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Proxy not responding" -ForegroundColor Red
        exit 1
    }
    
    # Interactive menu
    do {
        Write-Host "`n" + ("=" * 40) -ForegroundColor Cyan
        Write-Host "🔧 MODEL MANAGEMENT MENU" -ForegroundColor Cyan
        Write-Host ("=" * 40) -ForegroundColor Cyan
        Write-Host "1. Check model status" -ForegroundColor White
        Write-Host "2. Show model distribution" -ForegroundColor White
        Write-Host "3. Install model on all servers" -ForegroundColor White
        Write-Host "4. Quick install: tinyllama:latest" -ForegroundColor Yellow
        Write-Host "5. Quick install: llama3:latest" -ForegroundColor Yellow
        Write-Host "0. Exit" -ForegroundColor Gray
        
        $choice = Read-Host "`nEnter choice (0-5)"
        
        switch ($choice) {
            "1" {
                $modelName = Read-Host "Enter model name (e.g., 'llama3:latest')"
                if (![string]::IsNullOrWhiteSpace($modelName)) {
                    Get-ModelStatus -ModelName $modelName
                }
            }
            "2" {
                Show-ModelDistribution
            }
            "3" {
                $modelName = Read-Host "Enter model name to install (e.g., 'llama3:latest')"
                if (![string]::IsNullOrWhiteSpace($modelName)) {
                    $confirm = Read-Host "Install $modelName on all servers? (y/N)"
                    if ($confirm -eq "y" -or $confirm -eq "Y") {
                        $success = Install-ModelAllServers -ModelName $modelName
                        if ($success) {
                            Write-Host "`n✅ Installation complete! Verifying..." -ForegroundColor Green
                            Start-Sleep -Seconds 2
                            Get-ModelStatus -ModelName $modelName
                        }
                    }
                }
            }
            "4" {
                Write-Host "`n🦙 Installing tinyllama:latest (small, fast model)" -ForegroundColor Yellow
                $confirm = Read-Host "Continue? (y/N)"
                if ($confirm -eq "y" -or $confirm -eq "Y") {
                    Install-ModelAllServers -ModelName "tinyllama:latest"
                }
            }
            "5" {
                Write-Host "`n🦙 Installing llama3:latest (full-featured model)" -ForegroundColor Yellow
                Write-Host "⚠️  This is ~4.7GB and may take several minutes" -ForegroundColor Yellow
                $confirm = Read-Host "Continue? (y/N)"
                if ($confirm -eq "y" -or $confirm -eq "Y") {
                    Install-ModelAllServers -ModelName "llama3:latest"
                }
            }
            "0" {
                Write-Host "`n👋 Goodbye!" -ForegroundColor Green
                break
            }
            default {
                Write-Host "❌ Invalid choice" -ForegroundColor Red
            }
        }
        
        if ($choice -ne "0") {
            Read-Host "`nPress Enter to continue..."
        }
        
    } while ($choice -ne "0")
    
}
catch {
    Write-Host "`n❌ Error: $($_.Exception.Message)" -ForegroundColor Red
}
