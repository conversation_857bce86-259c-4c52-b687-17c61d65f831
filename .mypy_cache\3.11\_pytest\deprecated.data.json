{".class": "MypyFile", "_fullname": "_pytest.deprecated", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "DEPRECATED_EXTERNAL_PLUGINS": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "_pytest.deprecated.DEPRECATED_EXTERNAL_PLUGINS", "name": "DEPRECATED_EXTERNAL_PLUGINS", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.set"}}}, "HOOK_LEGACY_MARKING": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "_pytest.deprecated.HOOK_LEGACY_MARKING", "name": "HOOK_LEGACY_MARKING", "type": {".class": "Instance", "args": ["_pytest.warning_types.PytestDeprecationWarning"], "extra_attrs": null, "type_ref": "_pytest.warning_types.UnformattedWarning"}}}, "HOOK_LEGACY_PATH_ARG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "_pytest.deprecated.HOOK_LEGACY_PATH_ARG", "name": "HOOK_LEGACY_PATH_ARG", "type": {".class": "Instance", "args": ["_pytest.warning_types.PytestRemovedIn9Warning"], "extra_attrs": null, "type_ref": "_pytest.warning_types.UnformattedWarning"}}}, "MARKED_FIXTURE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "_pytest.deprecated.MARKED_FIXTURE", "name": "MARKED_FIXTURE", "type": "_pytest.warning_types.PytestRemovedIn9Warning"}}, "NODE_CTOR_FSPATH_ARG": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "_pytest.deprecated.NODE_CTOR_FSPATH_ARG", "name": "NODE_CTOR_FSPATH_ARG", "type": {".class": "Instance", "args": ["_pytest.warning_types.PytestRemovedIn9Warning"], "extra_attrs": null, "type_ref": "_pytest.warning_types.UnformattedWarning"}}}, "PRIVATE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "_pytest.deprecated.PRIVATE", "name": "PRIVATE", "type": "_pytest.warning_types.PytestDeprecationWarning"}}, "PytestDeprecationWarning": {".class": "SymbolTableNode", "cross_ref": "_pytest.warning_types.PytestDeprecationWarning", "kind": "Gdef"}, "PytestRemovedIn9Warning": {".class": "SymbolTableNode", "cross_ref": "_pytest.warning_types.PytestRemovedIn9Warning", "kind": "Gdef"}, "UnformattedWarning": {".class": "SymbolTableNode", "cross_ref": "_pytest.warning_types.UnformattedWarning", "kind": "Gdef"}, "YIELD_FIXTURE": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "_pytest.deprecated.YIELD_FIXTURE", "name": "YIELD_FIXTURE", "type": "_pytest.warning_types.PytestDeprecationWarning"}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.deprecated.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.deprecated.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.deprecated.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.deprecated.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.deprecated.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.deprecated.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "check_ispytest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["ispytest"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.deprecated.check_ispytest", "name": "check_ispytest", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["ispytest"], "arg_types": ["builtins.bool"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "check_ispytest", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "warn": {".class": "SymbolTableNode", "cross_ref": "_warnings.warn", "kind": "Gdef"}}, "path": "c:\\home-repos\\llm_proxy_server\\venv\\Lib\\site-packages\\_pytest\\deprecated.py"}