["tests/test_api_integration.py::TestAPITagsEndpoint::test_api_tags_empty_response", "tests/test_api_integration.py::TestAPITagsEndpoint::test_api_tags_endpoint", "tests/test_api_integration.py::TestAuthenticationIntegration::test_api_with_auth_enabled_no_credentials", "tests/test_api_integration.py::TestAuthenticationIntegration::test_api_with_invalid_credentials", "tests/test_api_integration.py::TestAuthenticationIntegration::test_api_with_valid_credentials", "tests/test_api_integration.py::TestAuthenticationIntegration::test_api_without_auth_disabled", "tests/test_api_integration.py::TestChatEndpoint::test_chat_endpoint_empty_messages", "tests/test_api_integration.py::TestChatEndpoint::test_chat_endpoint_invalid_request", "tests/test_api_integration.py::TestChatEndpoint::test_chat_endpoint_success", "tests/test_api_integration.py::TestErrorHandling::test_404_endpoint", "tests/test_api_integration.py::TestErrorHandling::test_malformed_json", "tests/test_api_integration.py::TestErrorHandling::test_method_not_allowed", "tests/test_api_integration.py::TestErrorHandling::test_model_not_found_error", "tests/test_api_integration.py::TestErrorHandling::test_server_error_handling", "tests/test_api_integration.py::TestGenerateEndpoint::test_generate_endpoint_invalid_request", "tests/test_api_integration.py::TestGenerateEndpoint::test_generate_endpoint_success", "tests/test_api_integration.py::TestHealthEndpoint::test_health_endpoint", "tests/test_api_integration.py::TestHealthEndpoint::test_health_endpoint_structure", "tests/test_api_integration.py::TestMetricsEndpoint::test_metrics_endpoint_with_auth_no_credentials", "tests/test_api_integration.py::TestMetricsEndpoint::test_metrics_endpoint_with_non_admin_credentials", "tests/test_api_integration.py::TestMetricsEndpoint::test_metrics_endpoint_with_valid_admin_credentials", "tests/test_api_integration.py::TestMetricsEndpoint::test_metrics_endpoint_without_auth", "tests/test_api_integration.py::TestProxyStatusEndpoint::test_proxy_status_endpoint", "tests/test_api_integration.py::TestStreamingEndpoints::test_chat_streaming_endpoint", "tests/test_api_integration.py::TestStreamingEndpoints::test_generate_streaming_endpoint", "tests/test_auth.py::TestAuthManager::test_authenticate_exception_handling", "tests/test_auth.py::TestAuthManager::test_authenticate_invalid_password", "tests/test_auth.py::TestAuthManager::test_authenticate_invalid_token_format", "tests/test_auth.py::TestAuthManager::test_authenticate_nonexistent_user", "tests/test_auth.py::TestAuthManager::test_authenticate_valid_regular_user", "tests/test_auth.py::TestAuthManager::test_authenticate_valid_user", "tests/test_auth.py::TestAuthManager::test_authenticate_with_colon_in_password", "tests/test_auth.py::TestAuthManager::test_get_user_count", "tests/test_auth.py::TestAuthManager::test_init_with_existing_file", "tests/test_auth.py::TestAuthManager::test_init_with_missing_file", "tests/test_auth.py::TestAuthManager::test_is_user_admin", "tests/test_auth.py::TestAuthManager::test_load_users_file_read_error", "tests/test_auth.py::TestAuthManager::test_load_users_minimal_format", "tests/test_auth.py::TestAuthManager::test_load_users_with_comments_and_empty_lines", "tests/test_auth.py::TestAuthManager::test_load_users_with_invalid_format", "tests/test_auth.py::TestAuthManager::test_reload_users", "tests/test_auth.py::TestAuthManagerEdgeCases::test_admin_privilege_escalation_prevention", "tests/test_auth.py::TestAuthManagerEdgeCases::test_authenticate_empty_credentials", "tests/test_auth.py::TestAuthManagerEdgeCases::test_authenticate_sql_injection_attempt", "tests/test_auth.py::TestAuthManagerEdgeCases::test_authenticate_timing_attack_resistance", "tests/test_auth.py::TestAuthManagerEdgeCases::test_authenticate_unicode_handling", "tests/test_auth.py::TestAuthManagerEdgeCases::test_authenticate_very_long_credentials", "tests/test_auth.py::TestAuthManagerEdgeCases::test_concurrent_user_loading", "tests/test_auth.py::TestAuthManagerEdgeCases::test_user_file_modification_detection", "tests/test_config.py::TestGetSettings::test_get_settings_returns_settings_instance", "tests/test_config.py::TestGetSettings::test_get_settings_singleton_behavior", "tests/test_config.py::TestGetSettings::test_get_settings_with_environment", "tests/test_config.py::TestSettings::test_boolean_environment_variables", "tests/test_config.py::TestSettings::test_custom_settings", "tests/test_config.py::TestSettings::test_default_settings", "tests/test_config.py::TestSettings::test_environment_variables", "tests/test_config.py::TestSettings::test_integer_environment_variables", "tests/test_config.py::TestSettings::test_invalid_port_type", "tests/test_config.py::TestSettings::test_settings_immutability", "tests/test_config.py::TestSettingsValidation::test_positive_integers", "tests/test_config.py::TestSettingsValidation::test_valid_load_balancer_strategies", "tests/test_config.py::TestSettingsValidation::test_valid_log_levels", "tests/test_config.py::TestSettingsValidation::test_zero_and_negative_values", "tests/test_load_balancer.py::TestLoadBalancer::test_add_host", "tests/test_load_balancer.py::TestLoadBalancer::test_add_multiple_hosts", "tests/test_load_balancer.py::TestLoadBalancer::test_all_hosts_unhealthy_fallback", "tests/test_load_balancer.py::TestLoadBalancer::test_decrement_connections_below_zero", "tests/test_load_balancer.py::TestLoadBalancer::test_enable_disable_host", "tests/test_load_balancer.py::TestLoadBalancer::test_get_host_stats", "tests/test_load_balancer.py::TestLoadBalancer::test_get_next_host_all_disabled", "tests/test_load_balancer.py::TestLoadBalancer::test_get_next_host_least_connections", "tests/test_load_balancer.py::TestLoadBalancer::test_get_next_host_no_hosts", "tests/test_load_balancer.py::TestLoadBalancer::test_get_next_host_round_robin", "tests/test_load_balancer.py::TestLoadBalancer::test_get_next_host_weighted_random", "tests/test_load_balancer.py::TestLoadBalancer::test_increment_decrement_connections", "tests/test_load_balancer.py::TestLoadBalancer::test_init_invalid_strategy", "tests/test_load_balancer.py::TestLoadBalancer::test_init_least_connections", "tests/test_load_balancer.py::TestLoadBalancer::test_init_round_robin", "tests/test_load_balancer.py::TestLoadBalancer::test_init_weighted_random", "tests/test_load_balancer.py::TestLoadBalancer::test_least_connections_selection", "tests/test_load_balancer.py::TestLoadBalancer::test_random_selection", "tests/test_load_balancer.py::TestLoadBalancer::test_remove_host", "tests/test_load_balancer.py::TestLoadBalancer::test_remove_nonexistent_host", "tests/test_load_balancer.py::TestLoadBalancer::test_reset_connections", "tests/test_load_balancer.py::TestLoadBalancer::test_reset_stats", "tests/test_load_balancer.py::TestLoadBalancer::test_round_robin_selection", "tests/test_load_balancer.py::TestLoadBalancer::test_select_host_no_hosts", "tests/test_load_balancer.py::TestLoadBalancer::test_select_host_single_host", "tests/test_load_balancer.py::TestLoadBalancer::test_set_host_health", "tests/test_load_balancer.py::TestLoadBalancer::test_set_host_weight", "tests/test_load_balancer.py::TestLoadBalancer::test_set_weight_nonexistent_host", "tests/test_load_balancer.py::TestLoadBalancer::test_unhealthy_hosts_filtered", "tests/test_load_balancer.py::TestLoadBalancer::test_weighted_random_selection", "tests/test_models.py::TestChatMessage::test_any_role_allowed", "tests/test_models.py::TestChatMessage::test_empty_content", "tests/test_models.py::TestChatMessage::test_invalid_role", "tests/test_models.py::TestChatMessage::test_valid_assistant_message", "tests/test_models.py::TestChatMessage::test_valid_system_message", "tests/test_models.py::TestChatMessage::test_valid_user_message", "tests/test_models.py::TestChatRequest::test_chat_request_defaults", "tests/test_models.py::TestChatRequest::test_chat_request_with_multiple_messages", "tests/test_models.py::TestChatRequest::test_empty_messages", "tests/test_models.py::TestChatRequest::test_empty_messages_allowed", "tests/test_models.py::TestChatRequest::test_valid_chat_request", "tests/test_models.py::TestGenerateRequest::test_empty_prompt", "tests/test_models.py::TestGenerateRequest::test_generate_request_defaults", "tests/test_models.py::TestGenerateRequest::test_valid_generate_request", "tests/test_models.py::TestHealthResponse::test_unhealthy_status", "tests/test_models.py::TestHealthResponse::test_valid_health_response", "tests/test_models.py::TestHostConfig::test_host_config_defaults", "tests/test_models.py::TestHostConfig::test_invalid_max_connections", "tests/test_models.py::TestHostConfig::test_invalid_weight", "tests/test_models.py::TestHostConfig::test_negative_weight_allowed", "tests/test_models.py::TestHostConfig::test_valid_host_config", "tests/test_models.py::TestHostConfig::test_zero_max_connections_allowed", "tests/test_models.py::TestModelInfo::test_model_info_empty_details", "tests/test_models.py::TestModelInfo::test_valid_model_info", "tests/test_models.py::TestModelValidation::test_model_deserialization", "tests/test_models.py::TestModelValidation::test_model_serialization", "tests/test_models.py::TestModelValidation::test_model_validation_errors", "tests/test_models.py::TestProxyStatus::test_valid_proxy_status", "tests/test_models.py::TestProxyStatus::test_zero_proxy_status", "tests/test_monitoring.py::TestMetricsManager::test_decrement_connections_below_zero", "tests/test_monitoring.py::TestMetricsManager::test_generate_metrics", "tests/test_monitoring.py::TestMetricsManager::test_get_health_status", "tests/test_monitoring.py::TestMetricsManager::test_get_host_stats", "tests/test_monitoring.py::TestMetricsManager::test_get_model_stats", "tests/test_monitoring.py::TestMetricsManager::test_get_recent_activity", "tests/test_monitoring.py::TestMetricsManager::test_get_request_stats", "tests/test_monitoring.py::TestMetricsManager::test_increment_decrement_active_connections", "tests/test_monitoring.py::TestMetricsManager::test_init", "tests/test_monitoring.py::TestMetricsManager::test_metrics_thread_safety", "tests/test_monitoring.py::TestMetricsManager::test_record_health_check", "tests/test_monitoring.py::TestMetricsManager::test_record_multiple_requests", "tests/test_monitoring.py::TestMetricsManager::test_record_request_error", "tests/test_monitoring.py::TestMetricsManager::test_record_request_success", "tests/test_monitoring.py::TestMetricsManager::test_reset_metrics", "tests/test_proxy_manager.py::TestProxyManager::test_discover_models", "tests/test_proxy_manager.py::TestProxyManager::test_get_client_for_model", "tests/test_proxy_manager.py::TestProxyManager::test_get_host_for_client", "tests/test_proxy_manager.py::TestProxyManager::test_get_status", "tests/test_proxy_manager.py::TestProxyManager::test_handle_chat_model_not_found", "tests/test_proxy_manager.py::TestProxyManager::test_handle_chat_stream", "tests/test_proxy_manager.py::TestProxyManager::test_handle_chat_success", "tests/test_proxy_manager.py::TestProxyManager::test_handle_generate_success", "tests/test_proxy_manager.py::TestProxyManager::test_init", "tests/test_proxy_manager.py::TestProxyManager::test_initialize_clients", "tests/test_proxy_manager.py::TestProxyManager::test_initialize_success", "tests/test_proxy_manager.py::TestProxyManager::test_list_models", "tests/test_proxy_manager.py::TestProxyManager::test_load_host_configs", "tests/test_proxy_manager.py::TestProxyManager::test_load_host_configs_file_not_found", "tests/test_proxy_manager.py::TestProxyManager::test_shutdown"]