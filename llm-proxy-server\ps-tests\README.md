# PowerShell Test Suite for LLM Proxy Server

This directory contains a consolidated set of PowerShell tests for the LLM Proxy Server.

## 🧪 Test Scripts

### `test-all.ps1` - Complete Test Suite
Runs all tests in sequence with interactive prompts.
```powershell
.\ps-tests\test-all.ps1
```

### `test-proxy-health.ps1` - Health Check
Basic proxy health and functionality verification.
- ✅ Health endpoint check
- 📊 Status and metrics verification  
- 📋 Available models listing
- 💬 Quick chat functionality test

```powershell
.\ps-tests\test-proxy-health.ps1
```

### `test-load-balancing.ps1` - Load Balancing Test
Tests different load balancing strategies and finds optimal configuration.
- 🔄 Tests round_robin, fastest, adaptive strategies
- 📊 Measures response times and distribution
- ⚙️ Automatically sets optimal strategy

```powershell
.\ps-tests\test-load-balancing.ps1
```

### `test-model-management.ps1` - Model Management
Interactive model management operations.
- 🔍 Check model status across servers
- 📊 Show model distribution analysis
- 📥 Install models on all servers
- 🦙 Quick install for common models

```powershell
.\ps-tests\test-model-management.ps1
```

## 🎯 Quick Start

1. **Run complete test suite:**
   ```powershell
   .\ps-tests\test-all.ps1
   ```

2. **Just check if everything is working:**
   ```powershell
   .\ps-tests\test-proxy-health.ps1
   ```

3. **Optimize load balancing:**
   ```powershell
   .\ps-tests\test-load-balancing.ps1
   ```

4. **Manage models:**
   ```powershell
   .\ps-tests\test-model-management.ps1
   ```

## 📋 Prerequisites

- LLM Proxy Server running (`docker-compose -f docker-compose.local.yml up -d`)
- PowerShell 5.1 or later
- Network access to proxy on `localhost:11440`

## 🔧 Server Mapping

The tests automatically map server IPs to friendly names:
- `************` → `lynn-pc`
- `************` → `home-game-server`  
- `************` → `t5810`

## 📊 Expected Results

### Health Check
- ✅ Proxy responds to health checks
- 📊 Status shows active servers
- 💬 Chat functionality works

### Load Balancing
- 🔄 Even distribution with round_robin
- ⚡ Fastest server routing with fastest/adaptive
- 📈 Performance optimization recommendations

### Model Management
- 📋 Shows which models are on which servers
- 🎯 Identifies models available for load balancing
- 📥 Enables easy model installation

## 🚨 Troubleshooting

### Proxy Not Responding
```powershell
# Start the proxy
docker-compose -f docker-compose.local.yml up -d

# Check logs
docker logs llm-proxy-server-local
```

### Model Not Found
```powershell
# Check available models
.\ps-tests\test-model-management.ps1
# Choose option 2 to see distribution
```

### Load Balancing Issues
```powershell
# Run load balancing test to diagnose
.\ps-tests\test-load-balancing.ps1
```

## 🎉 Success Indicators

- ✅ All health checks pass
- ⚡ Sub-second response times with optimal strategy
- 🔄 Even distribution or intelligent routing working
- 📊 Models available on multiple servers for redundancy
