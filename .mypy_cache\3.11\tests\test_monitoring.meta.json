{"data_mtime": 1753377536, "dep_lines": [5, 8, 3, 4, 6, 300, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 5, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["unittest.mock", "llm_proxy_server.monitoring", "pytest", "time", "datetime", "threading", "builtins", "_frozen_importlib", "_pytest", "_pytest.mark", "_pytest.mark.structures", "_typeshed", "abc", "llm_proxy_server", "types", "typing", "typing_extensions"], "hash": "8e403e09c8238b36dcac5f07a608a59cc50faa6a", "id": "tests.test_monitoring", "ignore_all": false, "interface_hash": "952884b5f403bf611617d1ba327b4cd309d14da1", "mtime": 1753378066, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\llm_proxy_server\\llm-proxy-server\\tests\\test_monitoring.py", "plugin_data": null, "size": 12853, "suppressed": [], "version_id": "1.15.0"}