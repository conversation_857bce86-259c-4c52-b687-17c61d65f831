# PowerShell script to test the local LLM Proxy Server setup
# Run this script after starting the Docker containers

Write-Host "🚀 Testing LLM Stack (Proxy + WebUI) Local Setup" -ForegroundColor Green
Write-Host "=================================================" -ForegroundColor Green

# Function to test HTTP endpoint
function Test-Endpoint {
    param(
        [string]$Url,
        [string]$Description,
        [string]$Method = "GET"
    )

    Write-Host "`n🔍 Testing: $Description" -ForegroundColor Yellow
    Write-Host "URL: $Url" -ForegroundColor Gray

    try {
        if ($Method -eq "GET") {
            $response = Invoke-RestMethod -Uri $Url -Method GET -TimeoutSec 10
        } else {
            $response = Invoke-RestMethod -Uri $Url -Method POST -TimeoutSec 10
        }
        Write-Host "✅ SUCCESS: $Description" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "❌ FAILED: $Description" -ForegroundColor Red
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

function Get-ServerFromHost {
    param([string]$HostIP)

    switch ($HostIP) {
        "************" { return "lynn-pc" }
        "************" { return "home-game-server" }
        "************" { return "t5810" }
        default { return "unknown ($HostIP)" }
    }
}

# Wait for services to be ready
Write-Host "`n⏳ Waiting for services to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Test proxy health endpoint
$proxyHealth = Test-Endpoint -Url "http://localhost:11440/proxy/health" -Description "Proxy Health Check"

# Test proxy metrics (skip since it requires admin auth - this is expected)
Write-Host "`n🔍 Testing: Proxy Metrics" -ForegroundColor Cyan
Write-Host "URL: http://localhost:11440/proxy/metrics" -ForegroundColor Gray
Write-Host "⚠️  SKIPPED: Proxy Metrics (requires admin authentication - this is normal)" -ForegroundColor Yellow
$proxyMetrics = $true  # Assume working if health check passes

# Test individual Ollama instances (actual servers)
Write-Host "`n🔍 Testing individual Ollama instances:" -ForegroundColor Yellow
$ollama1 = Test-Endpoint -Url "http://************:11434/api/tags" -Description "lynn-pc (************)"
$ollama2 = Test-Endpoint -Url "http://************:11434/api/tags" -Description "home-game-server (************)"
$ollama3 = Test-Endpoint -Url "http://************:11434/api/tags" -Description "t5810 (************)"

# Test proxy API endpoints
Write-Host "`n🔍 Testing proxy API endpoints:" -ForegroundColor Yellow
$apiTags = Test-Endpoint -Url "http://localhost:11440/api/tags" -Description "Proxy API Tags"

# Test Open WebUI
Write-Host "`n🔍 Testing Open WebUI:" -ForegroundColor Yellow
$webui = Test-Endpoint -Url "http://localhost:3000" -Description "Open WebUI Interface"

# Test actual chat functionality through the proxy
Write-Host "`n🤖 Testing Chat Functionality:" -ForegroundColor Yellow

function Test-ChatEndpoint {
    param(
        [string]$Model,
        [string]$Message = "Hello! Please respond with just 'OK' to confirm you're working.",
        [string]$Description
    )

    Write-Host "🔍 Testing: $Description" -ForegroundColor Cyan
    Write-Host "Model: $Model" -ForegroundColor Gray

    # Check if this is an embedding model
    $isEmbeddingModel = $Model -like "*embed*" -or $Model -like "*nomic*"

    if ($isEmbeddingModel) {
        Write-Host "Type: Embedding Model" -ForegroundColor Magenta
        Write-Host "Text: $Message" -ForegroundColor Gray

        try {
            $body = @{
                model = $Model
                prompt = $Message
            } | ConvertTo-Json -Depth 3

            $response = Invoke-RestMethod -Uri "http://localhost:11440/api/embed" -Method POST -Body $body -ContentType "application/json" -TimeoutSec 30

            if ($response -and $response.embedding -and $response.embedding.Count -gt 0) {
                Write-Host "✅ SUCCESS: $Description" -ForegroundColor Green
                Write-Host "Embedding dimensions: $($response.embedding.Count)" -ForegroundColor Gray
                Write-Host "Sample values: [$($response.embedding[0..2] -join ', ')]..." -ForegroundColor Gray

                # Show which server handled the request
                if ($response.proxy_server) {
                    $serverName = Get-ServerFromHost -HostIP $response.proxy_server
                    Write-Host "🖥️  Served by: $serverName ($($response.proxy_server))" -ForegroundColor Cyan
                }

                return $true
            } else {
                Write-Host "❌ FAILED: $Description - No valid embedding response" -ForegroundColor Red
                return $false
            }
        }
        catch {
            Write-Host "❌ FAILED: $Description" -ForegroundColor Red
            Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
            return $false
        }
    } else {
        Write-Host "Type: Chat Model" -ForegroundColor Blue
        Write-Host "Message: $Message" -ForegroundColor Gray

        try {
            $body = @{
                model = $Model
                messages = @(
                    @{
                        role = "user"
                        content = $Message
                    }
                )
                stream = $false
            } | ConvertTo-Json -Depth 3

            $response = Invoke-RestMethod -Uri "http://localhost:11440/api/chat" -Method POST -Body $body -ContentType "application/json" -TimeoutSec 30

            if ($response -and $response.message -and $response.message.content) {
                Write-Host "✅ SUCCESS: $Description" -ForegroundColor Green
                Write-Host "Response: $($response.message.content.Substring(0, [Math]::Min(100, $response.message.content.Length)))..." -ForegroundColor Gray

                # Show which server handled the request
                if ($response.proxy_server) {
                    $serverName = Get-ServerFromHost -HostIP $response.proxy_server
                    Write-Host "🖥️  Served by: $serverName ($($response.proxy_server))" -ForegroundColor Cyan
                }

                return $true
            } else {
                Write-Host "❌ FAILED: $Description - No valid response" -ForegroundColor Red
                return $false
            }
        }
        catch {
            Write-Host "❌ FAILED: $Description" -ForegroundColor Red
            Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
            return $false
        }
    }
}

# Get available models first
Write-Host "📋 Getting available models..." -ForegroundColor Cyan
try {
    $modelsResponse = Invoke-RestMethod -Uri "http://localhost:11440/api/tags" -Method GET -TimeoutSec 10
    $availableModels = $modelsResponse.models | Select-Object -ExpandProperty name

    if ($availableModels.Count -gt 0) {
        Write-Host "Found $($availableModels.Count) models: $($availableModels -join ', ')" -ForegroundColor Green

        # Test chat with the first few available models
        $chatTests = @()
        $testCount = [Math]::Min(3, $availableModels.Count)  # Test up to 3 models

        for ($i = 0; $i -lt $testCount; $i++) {
            $model = $availableModels[$i]
            $chatTest = Test-ChatEndpoint -Model $model -Description "Chat with $model"
            $chatTests += @{Name="Chat with $model"; Result=$chatTest}
        }

        # Test load balancing by making multiple requests
        if ($availableModels.Count -gt 0) {
            Write-Host "`n🔄 Testing Load Balancing (multiple requests):" -ForegroundColor Yellow
            $loadBalanceTests = @()

            for ($i = 1; $i -le 3; $i++) {
                $testModel = $availableModels[0]  # Use first available model
                Write-Host "Request $i/3 to $testModel..." -ForegroundColor Gray
                $lbTest = Test-ChatEndpoint -Model $testModel -Message "Test request #$i" -Description "Load Balance Test $i"
                $loadBalanceTests += @{Name="Load Balance Test $i"; Result=$lbTest}
                Start-Sleep -Seconds 1  # Brief pause between requests
            }
        }
    } else {
        Write-Host "⚠️  No models found. Make sure your Ollama servers have models installed." -ForegroundColor Yellow
        $chatTests = @()
        $loadBalanceTests = @()
    }
}
catch {
    Write-Host "❌ Failed to get available models: $($_.Exception.Message)" -ForegroundColor Red
    $chatTests = @()
    $loadBalanceTests = @()
}

# Summary
Write-Host "`n📊 Test Summary:" -ForegroundColor Cyan
Write-Host "===============" -ForegroundColor Cyan

# Combine all tests
$tests = @(
    @{Name="Proxy Health"; Result=$proxyHealth},
    @{Name="Proxy Metrics"; Result=$proxyMetrics},
    @{Name="Proxy API"; Result=$apiTags},
    @{Name="Open WebUI"; Result=$webui},
    @{Name="lynn-pc Ollama"; Result=$ollama1},
    @{Name="home-game-server Ollama"; Result=$ollama2},
    @{Name="t5810 Ollama"; Result=$ollama3}
)

# Add chat tests if they exist
if ($chatTests) {
    $tests += $chatTests
}

# Add load balance tests if they exist
if ($loadBalanceTests) {
    $tests += $loadBalanceTests
}

$passed = 0
$total = $tests.Count

foreach ($test in $tests) {
    $status = if ($test.Result) { "✅ PASS" } else { "❌ FAIL" }
    $color = if ($test.Result) { "Green" } else { "Red" }
    Write-Host "$($test.Name): $status" -ForegroundColor $color
    if ($test.Result) { $passed++ }
}

Write-Host "`nResults: $passed/$total tests passed" -ForegroundColor $(if ($passed -eq $total) { "Green" } else { "Yellow" })

if ($passed -eq $total) {
    Write-Host "`n🎉 All tests passed! Your local stack is working correctly." -ForegroundColor Green
    Write-Host "`n🌐 Ready to use:" -ForegroundColor White
    Write-Host "• Open WebUI: http://localhost:3000 (Start here!)" -ForegroundColor Green
    Write-Host "• Proxy Health: http://localhost:11440/proxy/health" -ForegroundColor Gray
    Write-Host "• Proxy Metrics: http://localhost:11440/proxy/metrics" -ForegroundColor Gray
} else {
    Write-Host "`n⚠️  Some tests failed. Check the Docker containers and try again." -ForegroundColor Yellow
    Write-Host "Run 'docker-compose -f docker-compose.local.yml logs' to see container logs." -ForegroundColor Gray
}

Write-Host "`n📝 Next steps:" -ForegroundColor Cyan
Write-Host "- Pull a model: docker exec ollama-main ollama pull llama2" -ForegroundColor Gray
Write-Host "- Test chat: curl -X POST http://localhost:11440/api/chat -d '{`"model`":`"llama2`",`"messages`":[{`"role`":`"user`",`"content`":`"Hello!`"}]}'" -ForegroundColor Gray
Write-Host "- View logs: docker-compose -f docker-compose.local.yml logs -f llm-proxy-server" -ForegroundColor Gray
