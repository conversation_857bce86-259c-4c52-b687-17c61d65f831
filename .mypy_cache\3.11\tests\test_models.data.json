{".class": "MypyFile", "_fullname": "tests.test_models", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "ChatMessage": {".class": "SymbolTableNode", "cross_ref": "llm_proxy_server.models.ChatMessage", "kind": "Gdef"}, "ChatRequest": {".class": "SymbolTableNode", "cross_ref": "llm_proxy_server.models.ChatRequest", "kind": "Gdef"}, "ChatResponse": {".class": "SymbolTableNode", "cross_ref": "llm_proxy_server.models.ChatResponse", "kind": "Gdef"}, "GenerateRequest": {".class": "SymbolTableNode", "cross_ref": "llm_proxy_server.models.GenerateRequest", "kind": "Gdef"}, "GenerateResponse": {".class": "SymbolTableNode", "cross_ref": "llm_proxy_server.models.GenerateResponse", "kind": "Gdef"}, "HealthResponse": {".class": "SymbolTableNode", "cross_ref": "llm_proxy_server.models.HealthResponse", "kind": "Gdef"}, "HostConfig": {".class": "SymbolTableNode", "cross_ref": "llm_proxy_server.models.HostConfig", "kind": "Gdef"}, "ModelInfo": {".class": "SymbolTableNode", "cross_ref": "llm_proxy_server.models.ModelInfo", "kind": "Gdef"}, "ModelsResponse": {".class": "SymbolTableNode", "cross_ref": "llm_proxy_server.models.ModelsResponse", "kind": "Gdef"}, "ProxyStatus": {".class": "SymbolTableNode", "cross_ref": "llm_proxy_server.models.ProxyStatus", "kind": "Gdef"}, "TestChatMessage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_models.TestChatMessage", "name": "TestChatMessage", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_models.TestChatMessage", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_models", "mro": ["tests.test_models.TestChatMessage", "builtins.object"], "names": {".class": "SymbolTable", "test_any_role_allowed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_models.TestChatMessage.test_any_role_allowed", "name": "test_any_role_allowed", "type": null}}, "test_empty_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_models.TestChatMessage.test_empty_content", "name": "test_empty_content", "type": null}}, "test_valid_assistant_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_models.TestChatMessage.test_valid_assistant_message", "name": "test_valid_assistant_message", "type": null}}, "test_valid_system_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_models.TestChatMessage.test_valid_system_message", "name": "test_valid_system_message", "type": null}}, "test_valid_user_message": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_models.TestChatMessage.test_valid_user_message", "name": "test_valid_user_message", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_models.TestChatMessage.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_models.TestChatMessage", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestChatRequest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_models.TestChatRequest", "name": "TestChatRequest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_models.TestChatRequest", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_models", "mro": ["tests.test_models.TestChatRequest", "builtins.object"], "names": {".class": "SymbolTable", "test_chat_request_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_models.TestChatRequest.test_chat_request_defaults", "name": "test_chat_request_defaults", "type": null}}, "test_chat_request_with_multiple_messages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_models.TestChatRequest.test_chat_request_with_multiple_messages", "name": "test_chat_request_with_multiple_messages", "type": null}}, "test_empty_messages_allowed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_models.TestChatRequest.test_empty_messages_allowed", "name": "test_empty_messages_allowed", "type": null}}, "test_valid_chat_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_models.TestChatRequest.test_valid_chat_request", "name": "test_valid_chat_request", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_models.TestChatRequest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_models.TestChatRequest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestGenerateRequest": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_models.TestGenerateRequest", "name": "TestGenerateRequest", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_models.TestGenerateRequest", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_models", "mro": ["tests.test_models.TestGenerateRequest", "builtins.object"], "names": {".class": "SymbolTable", "test_empty_prompt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_models.TestGenerateRequest.test_empty_prompt", "name": "test_empty_prompt", "type": null}}, "test_generate_request_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_models.TestGenerateRequest.test_generate_request_defaults", "name": "test_generate_request_defaults", "type": null}}, "test_valid_generate_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_models.TestGenerateRequest.test_valid_generate_request", "name": "test_valid_generate_request", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_models.TestGenerateRequest.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_models.TestGenerateRequest", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestHealthResponse": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_models.TestHealthResponse", "name": "TestHealthResponse", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_models.TestHealthResponse", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_models", "mro": ["tests.test_models.TestHealthResponse", "builtins.object"], "names": {".class": "SymbolTable", "test_unhealthy_status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_models.TestHealthResponse.test_unhealthy_status", "name": "test_unhealthy_status", "type": null}}, "test_valid_health_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_models.TestHealthResponse.test_valid_health_response", "name": "test_valid_health_response", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_models.TestHealthResponse.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_models.TestHealthResponse", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestHostConfig": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_models.TestHostConfig", "name": "TestHostConfig", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_models.TestHostConfig", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_models", "mro": ["tests.test_models.TestHostConfig", "builtins.object"], "names": {".class": "SymbolTable", "test_host_config_defaults": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_models.TestHostConfig.test_host_config_defaults", "name": "test_host_config_defaults", "type": null}}, "test_negative_weight_allowed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_models.TestHostConfig.test_negative_weight_allowed", "name": "test_negative_weight_allowed", "type": null}}, "test_valid_host_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_models.TestHostConfig.test_valid_host_config", "name": "test_valid_host_config", "type": null}}, "test_zero_max_connections_allowed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_models.TestHostConfig.test_zero_max_connections_allowed", "name": "test_zero_max_connections_allowed", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_models.TestHostConfig.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_models.TestHostConfig", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestModelInfo": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_models.TestModelInfo", "name": "TestModelInfo", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_models.TestModelInfo", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_models", "mro": ["tests.test_models.TestModelInfo", "builtins.object"], "names": {".class": "SymbolTable", "test_model_info_empty_details": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_models.TestModelInfo.test_model_info_empty_details", "name": "test_model_info_empty_details", "type": null}}, "test_valid_model_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_models.TestModelInfo.test_valid_model_info", "name": "test_valid_model_info", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_models.TestModelInfo.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_models.TestModelInfo", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestModelValidation": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_models.TestModelValidation", "name": "TestModelValidation", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_models.TestModelValidation", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_models", "mro": ["tests.test_models.TestModelValidation", "builtins.object"], "names": {".class": "SymbolTable", "test_model_deserialization": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_models.TestModelValidation.test_model_deserialization", "name": "test_model_deserialization", "type": null}}, "test_model_serialization": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_models.TestModelValidation.test_model_serialization", "name": "test_model_serialization", "type": null}}, "test_model_validation_errors": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_models.TestModelValidation.test_model_validation_errors", "name": "test_model_validation_errors", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_models.TestModelValidation.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_models.TestModelValidation", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestProxyStatus": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_models.TestProxyStatus", "name": "TestProxyStatus", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_models.TestProxyStatus", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_models", "mro": ["tests.test_models.TestProxyStatus", "builtins.object"], "names": {".class": "SymbolTable", "test_valid_proxy_status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_models.TestProxyStatus.test_valid_proxy_status", "name": "test_valid_proxy_status", "type": null}}, "test_zero_proxy_status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_models.TestProxyStatus.test_zero_proxy_status", "name": "test_zero_proxy_status", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_models.TestProxyStatus.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_models.TestProxyStatus", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ValidationError": {".class": "SymbolTableNode", "cross_ref": "pydantic.ValidationError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_models.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_models.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_models.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_models.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_models.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_models.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "datetime": {".class": "SymbolTableNode", "cross_ref": "datetime.datetime", "kind": "Gdef"}, "pytest": {".class": "SymbolTableNode", "cross_ref": "pytest", "kind": "Gdef"}}, "path": "C:\\home-repos\\llm_proxy_server\\llm-proxy-server\\tests\\test_models.py"}