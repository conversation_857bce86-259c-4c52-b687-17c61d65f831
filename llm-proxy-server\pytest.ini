[pytest]
# Pytest configuration for LLM Proxy Server
minversion = 7.0
addopts = 
    -ra
    --strict-markers
    --strict-config
    --cov=llm_proxy_server
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml
    -v
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow tests
    auth: Authentication tests
    config: Configuration tests
    api: API endpoint tests
    load_balancer: Load balancer tests
    monitoring: Monitoring tests
asyncio_mode = auto
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
