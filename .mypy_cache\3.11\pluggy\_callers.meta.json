{"data_mtime": 1753364169, "dep_lines": [7, 14, 15, 17, 5, 10, 12, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 10, 5, 30, 30], "dependencies": ["collections.abc", "pluggy._hooks", "pluggy._result", "pluggy._warnings", "__future__", "typing", "warnings", "builtins", "_frozen_importlib", "abc"], "hash": "c66e0456606d3213c5e726c7bcf9d8410a9ba074", "id": "pluggy._callers", "ignore_all": true, "interface_hash": "22aa5c709c7e54cfb129aa22308182bfa7e8cdf5", "mtime": 1753376604, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\llm_proxy_server\\venv\\Lib\\site-packages\\pluggy\\_callers.py", "plugin_data": null, "size": 5991, "suppressed": [], "version_id": "1.15.0"}