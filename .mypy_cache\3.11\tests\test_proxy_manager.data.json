{".class": "MypyFile", "_fullname": "tests.test_proxy_manager", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncMock": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.AsyncMock", "kind": "Gdef"}, "ChatMessage": {".class": "SymbolTableNode", "cross_ref": "llm_proxy_server.models.ChatMessage", "kind": "Gdef"}, "ChatRequest": {".class": "SymbolTableNode", "cross_ref": "llm_proxy_server.models.ChatRequest", "kind": "Gdef"}, "GenerateRequest": {".class": "SymbolTableNode", "cross_ref": "llm_proxy_server.models.GenerateRequest", "kind": "Gdef"}, "HostConfig": {".class": "SymbolTableNode", "cross_ref": "llm_proxy_server.models.HostConfig", "kind": "Gdef"}, "MagicMock": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.MagicMock", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "ProxyManager": {".class": "SymbolTableNode", "cross_ref": "llm_proxy_server.proxy_manager.ProxyManager", "kind": "Gdef"}, "TestProxyManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_proxy_manager.TestProxyManager", "name": "TestProxyManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_proxy_manager.TestProxyManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_proxy_manager", "mro": ["tests.test_proxy_manager.TestProxyManager", "builtins.object"], "names": {".class": "SymbolTable", "mock_ollama_client": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "tests.test_proxy_manager.TestProxyManager.mock_ollama_client", "name": "mock_ollama_client", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_proxy_manager.TestProxyManager.mock_ollama_client", "name": "mock_ollama_client", "type": "_pytest.fixtures.FixtureFunctionDefinition"}}}, "proxy_manager_with_mocks": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "test_settings", "load_balancer", "metrics_manager", "hosts_config_file"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "tests.test_proxy_manager.TestProxyManager.proxy_manager_with_mocks", "name": "proxy_manager_with_mocks", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_proxy_manager.TestProxyManager.proxy_manager_with_mocks", "name": "proxy_manager_with_mocks", "type": "_pytest.fixtures.FixtureFunctionDefinition"}}}, "test_discover_models": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "proxy_manager_with_mocks", "mock_ollama_client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_proxy_manager.TestProxyManager.test_discover_models", "name": "test_discover_models", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_proxy_manager.TestProxyManager.test_discover_models", "name": "test_discover_models", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_get_client_for_model": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "proxy_manager_with_mocks", "mock_ollama_client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_proxy_manager.TestProxyManager.test_get_client_for_model", "name": "test_get_client_for_model", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_proxy_manager.TestProxyManager.test_get_client_for_model", "name": "test_get_client_for_model", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_get_host_for_client": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "proxy_manager_with_mocks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_proxy_manager.TestProxyManager.test_get_host_for_client", "name": "test_get_host_for_client", "type": null}}, "test_get_status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "proxy_manager_with_mocks", "mock_ollama_client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_proxy_manager.TestProxyManager.test_get_status", "name": "test_get_status", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_proxy_manager.TestProxyManager.test_get_status", "name": "test_get_status", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_handle_chat_model_not_found": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "proxy_manager_with_mocks", "mock_ollama_client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_proxy_manager.TestProxyManager.test_handle_chat_model_not_found", "name": "test_handle_chat_model_not_found", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_proxy_manager.TestProxyManager.test_handle_chat_model_not_found", "name": "test_handle_chat_model_not_found", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_handle_chat_stream": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "proxy_manager_with_mocks", "mock_ollama_client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_proxy_manager.TestProxyManager.test_handle_chat_stream", "name": "test_handle_chat_stream", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_proxy_manager.TestProxyManager.test_handle_chat_stream", "name": "test_handle_chat_stream", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_handle_chat_success": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "proxy_manager_with_mocks", "mock_ollama_client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_proxy_manager.TestProxyManager.test_handle_chat_success", "name": "test_handle_chat_success", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_proxy_manager.TestProxyManager.test_handle_chat_success", "name": "test_handle_chat_success", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_handle_generate_success": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "proxy_manager_with_mocks", "mock_ollama_client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_proxy_manager.TestProxyManager.test_handle_generate_success", "name": "test_handle_generate_success", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_proxy_manager.TestProxyManager.test_handle_generate_success", "name": "test_handle_generate_success", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_init": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "proxy_manager_with_mocks"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_proxy_manager.TestProxyManager.test_init", "name": "test_init", "type": null}}, "test_initialize_clients": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "proxy_manager_with_mocks", "mock_ollama_client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_proxy_manager.TestProxyManager.test_initialize_clients", "name": "test_initialize_clients", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_proxy_manager.TestProxyManager.test_initialize_clients", "name": "test_initialize_clients", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_initialize_success": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "proxy_manager_with_mocks", "mock_ollama_client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_proxy_manager.TestProxyManager.test_initialize_success", "name": "test_initialize_success", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_proxy_manager.TestProxyManager.test_initialize_success", "name": "test_initialize_success", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_list_models": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "proxy_manager_with_mocks", "mock_ollama_client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_proxy_manager.TestProxyManager.test_list_models", "name": "test_list_models", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_proxy_manager.TestProxyManager.test_list_models", "name": "test_list_models", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_load_host_configs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "proxy_manager_with_mocks"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_proxy_manager.TestProxyManager.test_load_host_configs", "name": "test_load_host_configs", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_proxy_manager.TestProxyManager.test_load_host_configs", "name": "test_load_host_configs", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_load_host_configs_file_not_found": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "test_settings", "load_balancer", "metrics_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_proxy_manager.TestProxyManager.test_load_host_configs_file_not_found", "name": "test_load_host_configs_file_not_found", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_proxy_manager.TestProxyManager.test_load_host_configs_file_not_found", "name": "test_load_host_configs_file_not_found", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_shutdown": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "proxy_manager_with_mocks", "mock_ollama_client"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_proxy_manager.TestProxyManager.test_shutdown", "name": "test_shutdown", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_proxy_manager.TestProxyManager.test_shutdown", "name": "test_shutdown", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_proxy_manager.TestProxyManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_proxy_manager.TestProxyManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_proxy_manager.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_proxy_manager.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_proxy_manager.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_proxy_manager.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_proxy_manager.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_proxy_manager.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "json": {".class": "SymbolTableNode", "cross_ref": "json", "kind": "Gdef"}, "mock_open": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.mock_open", "kind": "Gdef"}, "patch": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.patch", "kind": "Gdef"}, "pytest": {".class": "SymbolTableNode", "cross_ref": "pytest", "kind": "Gdef"}}, "path": "C:\\home-repos\\llm_proxy_server\\llm-proxy-server\\tests\\test_proxy_manager.py"}