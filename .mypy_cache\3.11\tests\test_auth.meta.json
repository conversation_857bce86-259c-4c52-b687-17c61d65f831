{"data_mtime": 1753377485, "dep_lines": [5, 7, 3, 4, 347, 348, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["unittest.mock", "llm_proxy_server.auth", "pytest", "pathlib", "threading", "time", "builtins", "_frozen_importlib", "_io", "_pytest", "_pytest.mark", "_pytest.mark.structures", "_pytest.outcomes", "_typeshed", "abc", "io", "llm_proxy_server", "os", "typing", "typing_extensions", "unittest"], "hash": "4e30d0adbcb7d6ba91d4ade883bdb13da186f89f", "id": "tests.test_auth", "ignore_all": false, "interface_hash": "2005274bd6424aee6f3c64dd465d677b38359038", "mtime": 1753378352, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\llm_proxy_server\\llm-proxy-server\\tests\\test_auth.py", "plugin_data": null, "size": 16601, "suppressed": [], "version_id": "1.15.0"}