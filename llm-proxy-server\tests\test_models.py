"""Tests for Pydantic models"""

import pytest
from datetime import datetime
from pydantic import ValidationError

from llm_proxy_server.models import (
    HostConfig, ChatMessage, ChatRequest, ChatResponse,
    GenerateRequest, GenerateResponse, HealthResponse,
    ProxyStatus, ModelInfo, ModelsResponse
)


@pytest.mark.unit
class TestHostConfig:
    """Test HostConfig model"""
    
    def test_valid_host_config(self):
        """Test creating a valid host configuration"""
        config = HostConfig(
            id="test-host",
            host="http://localhost:11434",
            enabled=True,
            weight=1.5,
            max_connections=10,
            timeout=300
        )
        
        assert config.id == "test-host"
        assert config.host == "http://localhost:11434"
        assert config.enabled is True
        assert config.weight == 1.5
        assert config.max_connections == 10
        assert config.timeout == 300
    
    def test_host_config_defaults(self):
        """Test host configuration with default values"""
        config = HostConfig(
            id="test-host",
            host="http://localhost:11434"
        )
        
        assert config.enabled is True
        assert config.weight == 1.0
        assert config.max_connections == 10
        assert config.timeout == 300
    
    def test_negative_weight_allowed(self):
        """Test host configuration with negative weight (currently allowed)"""
        # The current model doesn't validate weight constraints
        config = HostConfig(
            id="test-host",
            host="http://localhost:11434",
            weight=-1.0
        )
        assert config.weight == -1.0

    def test_zero_max_connections_allowed(self):
        """Test host configuration with zero max_connections (currently allowed)"""
        # The current model doesn't validate max_connections constraints
        config = HostConfig(
            id="test-host",
            host="http://localhost:11434",
            max_connections=0
        )
        assert config.max_connections == 0


@pytest.mark.unit
class TestChatMessage:
    """Test ChatMessage model"""
    
    def test_valid_user_message(self):
        """Test creating a valid user message"""
        message = ChatMessage(
            role="user",
            content="Hello, how are you?"
        )
        
        assert message.role == "user"
        assert message.content == "Hello, how are you?"
    
    def test_valid_assistant_message(self):
        """Test creating a valid assistant message"""
        message = ChatMessage(
            role="assistant",
            content="I'm doing well, thank you!"
        )
        
        assert message.role == "assistant"
        assert message.content == "I'm doing well, thank you!"
    
    def test_valid_system_message(self):
        """Test creating a valid system message"""
        message = ChatMessage(
            role="system",
            content="You are a helpful assistant."
        )
        
        assert message.role == "system"
        assert message.content == "You are a helpful assistant."
    
    def test_any_role_allowed(self):
        """Test chat message with any role (currently allowed)"""
        # The current model doesn't validate role constraints
        message = ChatMessage(
            role="invalid_role",
            content="Some content"
        )
        assert message.role == "invalid_role"
    
    def test_empty_content(self):
        """Test chat message with empty content"""
        # Depending on validation rules, this might be valid or invalid
        try:
            message = ChatMessage(role="user", content="")
            assert message.content == ""
        except ValidationError:
            # If empty content is not allowed, that's also valid behavior
            pass


@pytest.mark.unit
class TestChatRequest:
    """Test ChatRequest model"""
    
    def test_valid_chat_request(self):
        """Test creating a valid chat request"""
        request = ChatRequest(
            model="llama2:latest",
            messages=[
                ChatMessage(role="user", content="Hello!")
            ],
            stream=False
        )
        
        assert request.model == "llama2:latest"
        assert len(request.messages) == 1
        assert request.messages[0].role == "user"
        assert request.stream is False
    
    def test_chat_request_with_multiple_messages(self):
        """Test chat request with multiple messages"""
        request = ChatRequest(
            model="llama2:latest",
            messages=[
                ChatMessage(role="system", content="You are helpful."),
                ChatMessage(role="user", content="Hello!"),
                ChatMessage(role="assistant", content="Hi there!"),
                ChatMessage(role="user", content="How are you?")
            ]
        )
        
        assert len(request.messages) == 4
        assert request.messages[0].role == "system"
        assert request.messages[-1].role == "user"
    
    def test_chat_request_defaults(self):
        """Test chat request with default values"""
        request = ChatRequest(
            model="llama2:latest",
            messages=[ChatMessage(role="user", content="Hello!")]
        )
        
        assert request.stream is False  # Default value
    
    def test_empty_messages_allowed(self):
        """Test chat request with empty messages (currently allowed)"""
        # The current model doesn't validate message list constraints
        request = ChatRequest(
            model="llama2:latest",
            messages=[]
        )
        assert request.messages == []


@pytest.mark.unit
class TestGenerateRequest:
    """Test GenerateRequest model"""
    
    def test_valid_generate_request(self):
        """Test creating a valid generate request"""
        request = GenerateRequest(
            model="llama2:latest",
            prompt="Tell me a joke",
            stream=False
        )
        
        assert request.model == "llama2:latest"
        assert request.prompt == "Tell me a joke"
        assert request.stream is False
    
    def test_generate_request_defaults(self):
        """Test generate request with default values"""
        request = GenerateRequest(
            model="llama2:latest",
            prompt="Test prompt"
        )
        
        assert request.stream is False  # Default value
    
    def test_empty_prompt(self):
        """Test generate request with empty prompt"""
        # Depending on validation, this might be valid or invalid
        try:
            request = GenerateRequest(model="llama2:latest", prompt="")
            assert request.prompt == ""
        except ValidationError:
            # If empty prompt is not allowed, that's also valid
            pass


@pytest.mark.unit
class TestHealthResponse:
    """Test HealthResponse model"""
    
    def test_valid_health_response(self):
        """Test creating a valid health response"""
        from datetime import datetime
        timestamp = datetime.fromisoformat("2024-01-01T00:00:00+00:00")

        response = HealthResponse(
            status="healthy",
            timestamp=timestamp,
            version="1.0.0",
            uptime=123.45
        )

        assert response.status == "healthy"
        assert response.timestamp == timestamp
        assert response.version == "1.0.0"
        assert response.uptime == 123.45
    
    def test_unhealthy_status(self):
        """Test health response with unhealthy status"""
        from datetime import datetime
        timestamp = datetime.fromisoformat("2024-01-01T00:00:00+00:00")

        response = HealthResponse(
            status="unhealthy",
            timestamp=timestamp,
            version="1.0.0",
            uptime=123.45
        )

        assert response.status == "unhealthy"


@pytest.mark.unit
class TestProxyStatus:
    """Test ProxyStatus model"""

    def test_valid_proxy_status(self):
        """Test creating a valid proxy status"""
        status = ProxyStatus(
            total_hosts=3,
            active_hosts=2,
            total_requests=100,
            failed_requests=5,
            average_response_time=0.25,
            hosts=[
                {"id": "host1", "status": "active"},
                {"id": "host2", "status": "inactive"}
            ]
        )

        assert status.total_hosts == 3
        assert status.active_hosts == 2
        assert status.total_requests == 100
        assert status.failed_requests == 5
        assert status.average_response_time == 0.25
        assert len(status.hosts) == 2

    def test_zero_proxy_status(self):
        """Test proxy status with zero values"""
        status = ProxyStatus(
            total_hosts=0,
            active_hosts=0,
            total_requests=0,
            failed_requests=0,
            average_response_time=0.0,
            hosts=[]
        )

        assert status.total_hosts == 0
        assert status.active_hosts == 0
        assert status.hosts == []


@pytest.mark.unit
class TestModelInfo:
    """Test ModelInfo model"""

    def test_valid_model_info(self):
        """Test creating valid model info"""
        model_info = ModelInfo(
            name="llama2:latest",
            model="llama2:latest",
            modified_at="2024-01-01T00:00:00Z",
            size=3826793677,
            digest="abc123",
            details={
                "format": "gguf",
                "family": "llama",
                "parameter_size": "7B"
            }
        )

        assert model_info.name == "llama2:latest"
        assert model_info.model == "llama2:latest"
        assert model_info.size == 3826793677
        assert model_info.digest == "abc123"
        assert "format" in model_info.details

    def test_model_info_empty_details(self):
        """Test model info with empty details"""
        model_info = ModelInfo(
            name="test:latest",
            model="test:latest",
            modified_at="2024-01-01T00:00:00Z",
            size=1000,
            digest="def456",
            details={}
        )

        assert model_info.details == {}


@pytest.mark.unit
class TestModelValidation:
    """Test model validation edge cases"""
    
    def test_model_serialization(self):
        """Test that models can be serialized to JSON"""
        config = HostConfig(
            id="test",
            host="http://localhost:11434"
        )
        
        json_data = config.model_dump()
        assert isinstance(json_data, dict)
        assert json_data["id"] == "test"
        assert json_data["host"] == "http://localhost:11434"
    
    def test_model_deserialization(self):
        """Test that models can be created from JSON"""
        json_data = {
            "id": "test",
            "host": "http://localhost:11434",
            "enabled": True,
            "weight": 1.0,
            "max_connections": 10,
            "timeout": 300
        }
        
        config = HostConfig(**json_data)
        assert config.id == "test"
        assert config.host == "http://localhost:11434"
    
    def test_model_validation_errors(self):
        """Test that validation errors are raised appropriately"""
        with pytest.raises(ValidationError):
            HostConfig()  # Missing required fields
        
        with pytest.raises(ValidationError):
            ChatMessage(role="user")  # Missing content
        
        with pytest.raises(ValidationError):
            ChatRequest(model="test")  # Missing messages
