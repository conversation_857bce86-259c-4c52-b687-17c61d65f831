["llm-proxy-server/tests/test_api_integration.py::TestAPITagsEndpoint::test_api_tags_empty_response", "llm-proxy-server/tests/test_api_integration.py::TestAPITagsEndpoint::test_api_tags_endpoint", "llm-proxy-server/tests/test_api_integration.py::TestAuthenticationIntegration::test_api_with_auth_enabled_no_credentials", "llm-proxy-server/tests/test_api_integration.py::TestAuthenticationIntegration::test_api_with_invalid_credentials", "llm-proxy-server/tests/test_api_integration.py::TestAuthenticationIntegration::test_api_with_valid_credentials", "llm-proxy-server/tests/test_api_integration.py::TestAuthenticationIntegration::test_api_without_auth_disabled", "llm-proxy-server/tests/test_api_integration.py::TestChatEndpoint::test_chat_endpoint_empty_messages", "llm-proxy-server/tests/test_api_integration.py::TestChatEndpoint::test_chat_endpoint_invalid_request", "llm-proxy-server/tests/test_api_integration.py::TestChatEndpoint::test_chat_endpoint_success", "llm-proxy-server/tests/test_api_integration.py::TestErrorHandling::test_404_endpoint", "llm-proxy-server/tests/test_api_integration.py::TestErrorHandling::test_malformed_json", "llm-proxy-server/tests/test_api_integration.py::TestErrorHandling::test_method_not_allowed", "llm-proxy-server/tests/test_api_integration.py::TestErrorHandling::test_model_not_found_error", "llm-proxy-server/tests/test_api_integration.py::TestErrorHandling::test_server_error_handling", "llm-proxy-server/tests/test_api_integration.py::TestGenerateEndpoint::test_generate_endpoint_invalid_request", "llm-proxy-server/tests/test_api_integration.py::TestGenerateEndpoint::test_generate_endpoint_success", "llm-proxy-server/tests/test_api_integration.py::TestHealthEndpoint::test_health_endpoint", "llm-proxy-server/tests/test_api_integration.py::TestHealthEndpoint::test_health_endpoint_structure", "llm-proxy-server/tests/test_api_integration.py::TestMetricsEndpoint::test_metrics_endpoint_with_auth_no_credentials", "llm-proxy-server/tests/test_api_integration.py::TestMetricsEndpoint::test_metrics_endpoint_with_non_admin_credentials", "llm-proxy-server/tests/test_api_integration.py::TestMetricsEndpoint::test_metrics_endpoint_with_valid_admin_credentials", "llm-proxy-server/tests/test_api_integration.py::TestMetricsEndpoint::test_metrics_endpoint_without_auth", "llm-proxy-server/tests/test_api_integration.py::TestProxyStatusEndpoint::test_proxy_status_endpoint", "llm-proxy-server/tests/test_api_integration.py::TestStreamingEndpoints::test_chat_streaming_endpoint", "llm-proxy-server/tests/test_api_integration.py::TestStreamingEndpoints::test_generate_streaming_endpoint", "llm-proxy-server/tests/test_auth.py::TestAuthManager::test_authenticate_exception_handling", "llm-proxy-server/tests/test_auth.py::TestAuthManager::test_authenticate_invalid_password", "llm-proxy-server/tests/test_auth.py::TestAuthManager::test_authenticate_invalid_token_format", "llm-proxy-server/tests/test_auth.py::TestAuthManager::test_authenticate_nonexistent_user", "llm-proxy-server/tests/test_auth.py::TestAuthManager::test_authenticate_valid_regular_user", "llm-proxy-server/tests/test_auth.py::TestAuthManager::test_authenticate_valid_user", "llm-proxy-server/tests/test_auth.py::TestAuthManager::test_authenticate_with_colon_in_password", "llm-proxy-server/tests/test_auth.py::TestAuthManager::test_get_user_count", "llm-proxy-server/tests/test_auth.py::TestAuthManager::test_init_with_existing_file", "llm-proxy-server/tests/test_auth.py::TestAuthManager::test_init_with_missing_file", "llm-proxy-server/tests/test_auth.py::TestAuthManager::test_is_user_admin", "llm-proxy-server/tests/test_auth.py::TestAuthManager::test_load_users_file_read_error", "llm-proxy-server/tests/test_auth.py::TestAuthManager::test_load_users_minimal_format", "llm-proxy-server/tests/test_auth.py::TestAuthManager::test_load_users_with_comments_and_empty_lines", "llm-proxy-server/tests/test_auth.py::TestAuthManager::test_load_users_with_invalid_format", "llm-proxy-server/tests/test_auth.py::TestAuthManager::test_reload_users", "llm-proxy-server/tests/test_auth.py::TestAuthManagerEdgeCases::test_admin_privilege_escalation_prevention", "llm-proxy-server/tests/test_auth.py::TestAuthManagerEdgeCases::test_authenticate_empty_credentials", "llm-proxy-server/tests/test_auth.py::TestAuthManagerEdgeCases::test_authenticate_sql_injection_attempt", "llm-proxy-server/tests/test_auth.py::TestAuthManagerEdgeCases::test_authenticate_timing_attack_resistance", "llm-proxy-server/tests/test_auth.py::TestAuthManagerEdgeCases::test_authenticate_unicode_handling", "llm-proxy-server/tests/test_auth.py::TestAuthManagerEdgeCases::test_authenticate_very_long_credentials", "llm-proxy-server/tests/test_auth.py::TestAuthManagerEdgeCases::test_concurrent_user_loading", "llm-proxy-server/tests/test_auth.py::TestAuthManagerEdgeCases::test_user_file_modification_detection", "llm-proxy-server/tests/test_config.py::TestGetSettings::test_get_settings_returns_settings_instance", "llm-proxy-server/tests/test_config.py::TestGetSettings::test_get_settings_singleton_behavior", "llm-proxy-server/tests/test_config.py::TestGetSettings::test_get_settings_with_environment", "llm-proxy-server/tests/test_config.py::TestSettings::test_boolean_environment_variables", "llm-proxy-server/tests/test_config.py::TestSettings::test_custom_settings", "llm-proxy-server/tests/test_config.py::TestSettings::test_default_settings", "llm-proxy-server/tests/test_config.py::TestSettings::test_environment_variables", "llm-proxy-server/tests/test_config.py::TestSettings::test_integer_environment_variables", "llm-proxy-server/tests/test_config.py::TestSettings::test_invalid_port_type", "llm-proxy-server/tests/test_config.py::TestSettings::test_settings_immutability", "llm-proxy-server/tests/test_config.py::TestSettingsValidation::test_positive_integers", "llm-proxy-server/tests/test_config.py::TestSettingsValidation::test_valid_load_balancer_strategies", "llm-proxy-server/tests/test_config.py::TestSettingsValidation::test_valid_log_levels", "llm-proxy-server/tests/test_config.py::TestSettingsValidation::test_zero_and_negative_values", "llm-proxy-server/tests/test_load_balancer.py::TestLoadBalancer::test_all_hosts_unhealthy_fallback", "llm-proxy-server/tests/test_load_balancer.py::TestLoadBalancer::test_decrement_connections_below_zero", "llm-proxy-server/tests/test_load_balancer.py::TestLoadBalancer::test_get_host_stats", "llm-proxy-server/tests/test_load_balancer.py::TestLoadBalancer::test_increment_decrement_connections", "llm-proxy-server/tests/test_load_balancer.py::TestLoadBalancer::test_init_invalid_strategy", "llm-proxy-server/tests/test_load_balancer.py::TestLoadBalancer::test_init_least_connections", "llm-proxy-server/tests/test_load_balancer.py::TestLoadBalancer::test_init_round_robin", "llm-proxy-server/tests/test_load_balancer.py::TestLoadBalancer::test_init_weighted_random", "llm-proxy-server/tests/test_load_balancer.py::TestLoadBalancer::test_least_connections_selection", "llm-proxy-server/tests/test_load_balancer.py::TestLoadBalancer::test_random_selection", "llm-proxy-server/tests/test_load_balancer.py::TestLoadBalancer::test_reset_stats", "llm-proxy-server/tests/test_load_balancer.py::TestLoadBalancer::test_round_robin_selection", "llm-proxy-server/tests/test_load_balancer.py::TestLoadBalancer::test_select_host_no_hosts", "llm-proxy-server/tests/test_load_balancer.py::TestLoadBalancer::test_select_host_single_host", "llm-proxy-server/tests/test_load_balancer.py::TestLoadBalancer::test_set_host_health", "llm-proxy-server/tests/test_load_balancer.py::TestLoadBalancer::test_set_host_weight", "llm-proxy-server/tests/test_load_balancer.py::TestLoadBalancer::test_unhealthy_hosts_filtered", "llm-proxy-server/tests/test_load_balancer.py::TestLoadBalancer::test_weighted_random_selection", "llm-proxy-server/tests/test_models.py::TestChatMessage::test_any_role_allowed", "llm-proxy-server/tests/test_models.py::TestChatMessage::test_empty_content", "llm-proxy-server/tests/test_models.py::TestChatMessage::test_valid_assistant_message", "llm-proxy-server/tests/test_models.py::TestChatMessage::test_valid_system_message", "llm-proxy-server/tests/test_models.py::TestChatMessage::test_valid_user_message", "llm-proxy-server/tests/test_models.py::TestChatRequest::test_chat_request_defaults", "llm-proxy-server/tests/test_models.py::TestChatRequest::test_chat_request_with_multiple_messages", "llm-proxy-server/tests/test_models.py::TestChatRequest::test_empty_messages_allowed", "llm-proxy-server/tests/test_models.py::TestChatRequest::test_valid_chat_request", "llm-proxy-server/tests/test_models.py::TestGenerateRequest::test_empty_prompt", "llm-proxy-server/tests/test_models.py::TestGenerateRequest::test_generate_request_defaults", "llm-proxy-server/tests/test_models.py::TestGenerateRequest::test_valid_generate_request", "llm-proxy-server/tests/test_models.py::TestHealthResponse::test_unhealthy_status", "llm-proxy-server/tests/test_models.py::TestHealthResponse::test_valid_health_response", "llm-proxy-server/tests/test_models.py::TestHostConfig::test_host_config_defaults", "llm-proxy-server/tests/test_models.py::TestHostConfig::test_negative_weight_allowed", "llm-proxy-server/tests/test_models.py::TestHostConfig::test_valid_host_config", "llm-proxy-server/tests/test_models.py::TestHostConfig::test_zero_max_connections_allowed", "llm-proxy-server/tests/test_models.py::TestModelInfo::test_model_info_empty_details", "llm-proxy-server/tests/test_models.py::TestModelInfo::test_valid_model_info", "llm-proxy-server/tests/test_models.py::TestModelValidation::test_model_deserialization", "llm-proxy-server/tests/test_models.py::TestModelValidation::test_model_serialization", "llm-proxy-server/tests/test_models.py::TestModelValidation::test_model_validation_errors", "llm-proxy-server/tests/test_models.py::TestProxyStatus::test_valid_proxy_status", "llm-proxy-server/tests/test_models.py::TestProxyStatus::test_zero_proxy_status", "llm-proxy-server/tests/test_monitoring.py::TestMetricsManager::test_decrement_connections_below_zero", "llm-proxy-server/tests/test_monitoring.py::TestMetricsManager::test_generate_metrics", "llm-proxy-server/tests/test_monitoring.py::TestMetricsManager::test_get_health_status", "llm-proxy-server/tests/test_monitoring.py::TestMetricsManager::test_get_host_stats", "llm-proxy-server/tests/test_monitoring.py::TestMetricsManager::test_get_model_stats", "llm-proxy-server/tests/test_monitoring.py::TestMetricsManager::test_get_recent_activity", "llm-proxy-server/tests/test_monitoring.py::TestMetricsManager::test_get_request_stats", "llm-proxy-server/tests/test_monitoring.py::TestMetricsManager::test_increment_decrement_active_connections", "llm-proxy-server/tests/test_monitoring.py::TestMetricsManager::test_init", "llm-proxy-server/tests/test_monitoring.py::TestMetricsManager::test_metrics_thread_safety", "llm-proxy-server/tests/test_monitoring.py::TestMetricsManager::test_record_health_check", "llm-proxy-server/tests/test_monitoring.py::TestMetricsManager::test_record_multiple_requests", "llm-proxy-server/tests/test_monitoring.py::TestMetricsManager::test_record_request_error", "llm-proxy-server/tests/test_monitoring.py::TestMetricsManager::test_record_request_success", "llm-proxy-server/tests/test_monitoring.py::TestMetricsManager::test_reset_metrics", "llm-proxy-server/tests/test_proxy_manager.py::TestProxyManager::test_discover_models", "llm-proxy-server/tests/test_proxy_manager.py::TestProxyManager::test_get_client_for_model", "llm-proxy-server/tests/test_proxy_manager.py::TestProxyManager::test_get_host_for_client", "llm-proxy-server/tests/test_proxy_manager.py::TestProxyManager::test_get_status", "llm-proxy-server/tests/test_proxy_manager.py::TestProxyManager::test_handle_chat_model_not_found", "llm-proxy-server/tests/test_proxy_manager.py::TestProxyManager::test_handle_chat_stream", "llm-proxy-server/tests/test_proxy_manager.py::TestProxyManager::test_handle_chat_success", "llm-proxy-server/tests/test_proxy_manager.py::TestProxyManager::test_handle_generate_success", "llm-proxy-server/tests/test_proxy_manager.py::TestProxyManager::test_init", "llm-proxy-server/tests/test_proxy_manager.py::TestProxyManager::test_initialize_clients", "llm-proxy-server/tests/test_proxy_manager.py::TestProxyManager::test_initialize_success", "llm-proxy-server/tests/test_proxy_manager.py::TestProxyManager::test_list_models", "llm-proxy-server/tests/test_proxy_manager.py::TestProxyManager::test_load_host_configs", "llm-proxy-server/tests/test_proxy_manager.py::TestProxyManager::test_load_host_configs_file_not_found", "llm-proxy-server/tests/test_proxy_manager.py::TestProxyManager::test_shutdown"]