version: '3.8'

services:

  # LLM Proxy Server
  llm-proxy-server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: llm-proxy-server-local
    restart: unless-stopped
    ports:
      - "11440:11434"  # Local testing port (avoiding conflict with existing service)
    environment:
      - HOST=0.0.0.0
      - PORT=11434
      - DEBUG=true  # Enable debug for local testing
      - AUTH_ENABLED=false  # Disabled by default now
      - AUTH_CONFIG_PATH=/app/config/authorized_users.txt
      - HOSTS_CONFIG_PATH=/app/config/hosts.local.json
      - LOAD_BALANCER_STRATEGY=adaptive  # Options: fastest, adaptive, least_connections, round_robin, random
      - METRICS_ENABLED=true
      - LOG_LEVEL=DEBUG  # More verbose logging for testing
      - MAX_CONCURRENT_REQUESTS=100
      - REQUEST_TIMEOUT=300
      - HEALTH_CHECK_INTERVAL=30
      - HEALTH_CHECK_TIMEOUT=10
    volumes:
      - ./config:/app/config:ro
      - ./logs:/app/logs
    networks:
      - ollama-network
    # No dependencies since we're using external Ollama services
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/proxy/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Open WebUI - Web interface for LLM interaction
  open-webui:
    image: ghcr.io/open-webui/open-webui:main
    container_name: open-webui-local
    restart: unless-stopped
    ports:
      - "3000:8080"  # Web interface port
    environment:
      - OLLAMA_BASE_URL=http://llm-proxy-server:11434  # Point to our proxy server
      - WEBUI_SECRET_KEY=your-secret-key-here
      - WEBUI_AUTH=false  # Disable auth for local testing (optional)
      - ENABLE_SIGNUP=true  # Allow user registration
      - DEFAULT_USER_ROLE=user
      - ENABLE_LOGIN_FORM=true
      - ENABLE_COMMUNITY_SHARING=false  # Disable for local testing
      - WEBUI_NAME="LLM Proxy Test Interface"
    volumes:
      - open-webui-data:/app/backend/data
    networks:
      - ollama-network
    depends_on:
      - llm-proxy-server
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

networks:
  ollama-network:
    driver: bridge

volumes:
  open-webui-data:
    driver: local
