# Testing Guide for LLM Proxy Server

This guide explains how to run tests for the LLM Proxy Server using VS Code's testing interface and command line tools.

## Test Structure

```
tests/
├── __init__.py
├── conftest.py              # Pytest fixtures and configuration
├── test_config.py           # Configuration management tests
├── test_auth.py             # Authentication tests
├── test_load_balancer.py    # Load balancer tests
├── test_models.py           # Pydantic model tests
└── test_api_integration.py  # API integration tests
```

## VS Code Testing Interface

### Setup
1. **Install Dependencies**: Make sure pytest and testing dependencies are installed:
   ```bash
   pip install -r requirements.txt
   ```

2. **VS Code Configuration**: The project includes `.vscode/settings.json` with pytest configuration.

3. **Python Interpreter**: Ensure VS Code is using the correct Python interpreter (the one in your virtual environment).

### Running Tests in VS Code

1. **Open Test Explorer**:
   - Press `Ctrl+Shift+P` (Windows/Linux) or `Cmd+Shift+P` (Mac)
   - Type "Test: Focus on Test Explorer View"
   - Or click the test beaker icon in the Activity Bar

2. **Discover Tests**:
   - Click the "Refresh Tests" button in the Test Explorer
   - VS Code will automatically discover all pytest tests

3. **Run Tests**:
   - **All Tests**: Click the play button at the top of Test Explorer
   - **Single Test**: Click the play button next to a specific test
   - **Test Class**: Click the play button next to a test class
   - **Test File**: Click the play button next to a test file

4. **Debug Tests**:
   - Click the debug icon next to any test to run it in debug mode
   - Set breakpoints in your test code or application code

5. **View Results**:
   - Test results appear in the Test Explorer with ✅ (pass) or ❌ (fail) icons
   - Click on failed tests to see error details
   - Output appears in the "Test Results" panel

## Command Line Testing

### Basic Commands

```bash
# Run all tests
python -m pytest

# Run with verbose output
python -m pytest -v

# Run specific test file
python -m pytest tests/test_config.py

# Run specific test class
python -m pytest tests/test_config.py::TestSettings

# Run specific test method
python -m pytest tests/test_config.py::TestSettings::test_default_settings

# Run tests with coverage
python -m pytest --cov=llm_proxy_server --cov-report=html

# Run only unit tests
python -m pytest -m unit

# Run only integration tests
python -m pytest -m integration

# Run tests matching a pattern
python -m pytest -k "test_auth"
```

### Using the Test Runner Script

```bash
# Run all tests with coverage
python run_tests.py --coverage

# Run only unit tests
python run_tests.py --unit

# Run only integration tests
python run_tests.py --integration

# Run tests for specific module
python run_tests.py --module config

# Run with verbose output
python run_tests.py --verbose

# Skip slow tests
python run_tests.py --fast

# Run tests matching pattern
python run_tests.py --pattern "test_load_balancer"
```

## Test Categories

Tests are organized with pytest markers:

- `@pytest.mark.unit` - Fast unit tests
- `@pytest.mark.integration` - Integration tests that may be slower
- `@pytest.mark.auth` - Authentication-related tests
- `@pytest.mark.config` - Configuration tests
- `@pytest.mark.load_balancer` - Load balancer tests
- `@pytest.mark.api` - API endpoint tests
- `@pytest.mark.slow` - Tests that take longer to run

## Coverage Reports

After running tests with coverage, you can view the HTML report:

1. **Generate Coverage**: Run tests with `--cov` flag
2. **View Report**: Open `htmlcov/index.html` in your browser
3. **Analyze**: Click on files to see line-by-line coverage

## Writing New Tests

### Test File Structure

```python
"""Tests for [module name]"""

import pytest
from unittest.mock import patch, MagicMock

from llm_proxy_server.[module] import [ClassToTest]


@pytest.mark.unit
@pytest.mark.[category]
class Test[ClassName]:
    """Test [ClassName] class"""
    
    def test_[functionality](self, [fixtures]):
        """Test [specific functionality]"""
        # Arrange
        # Act
        # Assert
```

### Using Fixtures

Common fixtures available in `conftest.py`:

- `temp_dir` - Temporary directory for test files
- `test_settings` - Test configuration settings
- `auth_manager` - AuthManager instance
- `load_balancer` - LoadBalancer instance
- `sample_hosts_config` - Sample host configuration data
- `mock_ollama_client` - Mock Ollama client

### Async Tests

For testing async functions:

```python
@pytest.mark.asyncio
async def test_async_function(self):
    """Test async functionality"""
    result = await some_async_function()
    assert result is not None
```

## Troubleshooting

### Common Issues

1. **Tests Not Discovered**:
   - Check that VS Code is using the correct Python interpreter
   - Ensure pytest is installed in the active environment
   - Check that test files follow naming convention (`test_*.py`)

2. **Import Errors**:
   - Verify that the project root is in Python path
   - Check that `__init__.py` files exist in test directories
   - Ensure all dependencies are installed

3. **Fixture Errors**:
   - Check that fixtures are properly defined in `conftest.py`
   - Verify fixture scope and dependencies

4. **Async Test Issues**:
   - Ensure `pytest-asyncio` is installed
   - Use `@pytest.mark.asyncio` decorator for async tests

### Debug Tips

1. **Use Print Statements**: Add `print()` statements in tests for debugging
2. **Use Debugger**: Set breakpoints and use VS Code's debugger
3. **Check Logs**: Look at test output and error messages
4. **Isolate Tests**: Run individual tests to isolate issues

## Continuous Integration

The test suite is designed to work with CI/CD pipelines:

```yaml
# Example GitHub Actions workflow
- name: Run Tests
  run: |
    python -m pytest --cov=llm_proxy_server --cov-report=xml
    
- name: Upload Coverage
  uses: codecov/codecov-action@v1
  with:
    file: ./coverage.xml
```

## Best Practices

1. **Test Naming**: Use descriptive test names that explain what is being tested
2. **Test Organization**: Group related tests in classes
3. **Fixtures**: Use fixtures for common setup code
4. **Mocking**: Mock external dependencies to keep tests fast and reliable
5. **Coverage**: Aim for high test coverage but focus on critical paths
6. **Documentation**: Document complex test scenarios
7. **Isolation**: Each test should be independent and not rely on other tests
