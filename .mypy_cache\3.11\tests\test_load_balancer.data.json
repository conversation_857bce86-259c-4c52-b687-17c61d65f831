{".class": "MypyFile", "_fullname": "tests.test_load_balancer", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "LoadBalancer": {".class": "SymbolTableNode", "cross_ref": "llm_proxy_server.load_balancer.LoadBalancer", "kind": "Gdef"}, "MagicMock": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.MagicMock", "kind": "Gdef"}, "TestLoadBalancer": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_load_balancer.TestLoadBalancer", "name": "TestLoadBalancer", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_load_balancer.TestLoadBalancer", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_load_balancer", "mro": ["tests.test_load_balancer.TestLoadBalancer", "builtins.object"], "names": {".class": "SymbolTable", "test_add_host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "load_balancer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_load_balancer.TestLoadBalancer.test_add_host", "name": "test_add_host", "type": null}}, "test_add_multiple_hosts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "load_balancer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_load_balancer.TestLoadBalancer.test_add_multiple_hosts", "name": "test_add_multiple_hosts", "type": null}}, "test_decrement_connections_below_zero": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "load_balancer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_load_balancer.TestLoadBalancer.test_decrement_connections_below_zero", "name": "test_decrement_connections_below_zero", "type": null}}, "test_enable_disable_host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "load_balancer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_load_balancer.TestLoadBalancer.test_enable_disable_host", "name": "test_enable_disable_host", "type": null}}, "test_get_host_stats": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "load_balancer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_load_balancer.TestLoadBalancer.test_get_host_stats", "name": "test_get_host_stats", "type": null}}, "test_get_next_host_all_disabled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "load_balancer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_load_balancer.TestLoadBalancer.test_get_next_host_all_disabled", "name": "test_get_next_host_all_disabled", "type": null}}, "test_get_next_host_least_connections": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_load_balancer.TestLoadBalancer.test_get_next_host_least_connections", "name": "test_get_next_host_least_connections", "type": null}}, "test_get_next_host_no_hosts": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "load_balancer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_load_balancer.TestLoadBalancer.test_get_next_host_no_hosts", "name": "test_get_next_host_no_hosts", "type": null}}, "test_get_next_host_round_robin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_load_balancer.TestLoadBalancer.test_get_next_host_round_robin", "name": "test_get_next_host_round_robin", "type": null}}, "test_get_next_host_weighted_random": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_load_balancer.TestLoadBalancer.test_get_next_host_weighted_random", "name": "test_get_next_host_weighted_random", "type": null}}, "test_increment_decrement_connections": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "load_balancer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_load_balancer.TestLoadBalancer.test_increment_decrement_connections", "name": "test_increment_decrement_connections", "type": null}}, "test_init_invalid_strategy": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_load_balancer.TestLoadBalancer.test_init_invalid_strategy", "name": "test_init_invalid_strategy", "type": null}}, "test_init_least_connections": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_load_balancer.TestLoadBalancer.test_init_least_connections", "name": "test_init_least_connections", "type": null}}, "test_init_round_robin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_load_balancer.TestLoadBalancer.test_init_round_robin", "name": "test_init_round_robin", "type": null}}, "test_init_weighted_random": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_load_balancer.TestLoadBalancer.test_init_weighted_random", "name": "test_init_weighted_random", "type": null}}, "test_remove_host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "load_balancer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_load_balancer.TestLoadBalancer.test_remove_host", "name": "test_remove_host", "type": null}}, "test_remove_nonexistent_host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "load_balancer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_load_balancer.TestLoadBalancer.test_remove_nonexistent_host", "name": "test_remove_nonexistent_host", "type": null}}, "test_reset_connections": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "load_balancer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_load_balancer.TestLoadBalancer.test_reset_connections", "name": "test_reset_connections", "type": null}}, "test_set_host_weight": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "load_balancer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_load_balancer.TestLoadBalancer.test_set_host_weight", "name": "test_set_host_weight", "type": null}}, "test_set_weight_nonexistent_host": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "load_balancer"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_load_balancer.TestLoadBalancer.test_set_weight_nonexistent_host", "name": "test_set_weight_nonexistent_host", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_load_balancer.TestLoadBalancer.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_load_balancer.TestLoadBalancer", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_load_balancer.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_load_balancer.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_load_balancer.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_load_balancer.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_load_balancer.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_load_balancer.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "pytest": {".class": "SymbolTableNode", "cross_ref": "pytest", "kind": "Gdef"}}, "path": "C:\\home-repos\\llm_proxy_server\\llm-proxy-server\\tests\\test_load_balancer.py"}