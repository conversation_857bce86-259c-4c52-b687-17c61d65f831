{".class": "MypyFile", "_fullname": "tests.test_api_integration", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AsyncMock": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.AsyncMock", "kind": "Gdef"}, "MagicMock": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.MagicMock", "kind": "Gdef"}, "TestAPITagsEndpoint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_api_integration.TestAPITagsEndpoint", "name": "TestAPITagsEndpoint", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_api_integration.TestAPITagsEndpoint", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_api_integration", "mro": ["tests.test_api_integration.TestAPITagsEndpoint", "builtins.object"], "names": {".class": "SymbolTable", "test_api_tags_empty_response": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mock_list_models"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "tests.test_api_integration.TestAPITagsEndpoint.test_api_tags_empty_response", "name": "test_api_tags_empty_response", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_api_integration.TestAPITagsEndpoint.test_api_tags_empty_response", "name": "test_api_tags_empty_response", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "test_api_tags_empty_response of TestAPITagsEndpoint", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_api_tags_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mock_list_models"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "tests.test_api_integration.TestAPITagsEndpoint.test_api_tags_endpoint", "name": "test_api_tags_endpoint", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_api_integration.TestAPITagsEndpoint.test_api_tags_endpoint", "name": "test_api_tags_endpoint", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "test_api_tags_endpoint of TestAPITagsEndpoint", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_api_integration.TestAPITagsEndpoint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_api_integration.TestAPITagsEndpoint", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestAuthenticationIntegration": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_api_integration.TestAuthenticationIntegration", "name": "TestAuthenticationIntegration", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_api_integration.TestAuthenticationIntegration", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_api_integration", "mro": ["tests.test_api_integration.TestAuthenticationIntegration", "builtins.object"], "names": {".class": "SymbolTable", "test_api_with_auth_enabled_no_credentials": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_api_integration.TestAuthenticationIntegration.test_api_with_auth_enabled_no_credentials", "name": "test_api_with_auth_enabled_no_credentials", "type": null}}, "test_api_with_invalid_credentials": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_api_integration.TestAuthenticationIntegration.test_api_with_invalid_credentials", "name": "test_api_with_invalid_credentials", "type": null}}, "test_api_with_valid_credentials": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_api_integration.TestAuthenticationIntegration.test_api_with_valid_credentials", "name": "test_api_with_valid_credentials", "type": null}}, "test_api_without_auth_disabled": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_api_integration.TestAuthenticationIntegration.test_api_without_auth_disabled", "name": "test_api_without_auth_disabled", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_api_integration.TestAuthenticationIntegration.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_api_integration.TestAuthenticationIntegration", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestChatEndpoint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_api_integration.TestChatEndpoint", "name": "TestChatEndpoint", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_api_integration.TestChatEndpoint", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_api_integration", "mro": ["tests.test_api_integration.TestChatEndpoint", "builtins.object"], "names": {".class": "SymbolTable", "test_chat_endpoint_empty_messages": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_api_integration.TestChatEndpoint.test_chat_endpoint_empty_messages", "name": "test_chat_endpoint_empty_messages", "type": null}}, "test_chat_endpoint_invalid_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_api_integration.TestChatEndpoint.test_chat_endpoint_invalid_request", "name": "test_chat_endpoint_invalid_request", "type": null}}, "test_chat_endpoint_success": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mock_handle_chat"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "tests.test_api_integration.TestChatEndpoint.test_chat_endpoint_success", "name": "test_chat_endpoint_success", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_api_integration.TestChatEndpoint.test_chat_endpoint_success", "name": "test_chat_endpoint_success", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "test_chat_endpoint_success of TestChatEndpoint", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_api_integration.TestChatEndpoint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_api_integration.TestChatEndpoint", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestClient": {".class": "SymbolTableNode", "cross_ref": "starlette.testclient.TestClient", "kind": "Gdef"}, "TestErrorHandling": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_api_integration.TestErrorHandling", "name": "TestError<PERSON><PERSON>ling", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_api_integration.TestErrorHandling", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_api_integration", "mro": ["tests.test_api_integration.TestErrorHandling", "builtins.object"], "names": {".class": "SymbolTable", "test_404_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_api_integration.TestErrorHandling.test_404_endpoint", "name": "test_404_endpoint", "type": null}}, "test_malformed_json": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_api_integration.TestErrorHandling.test_malformed_json", "name": "test_malformed_json", "type": null}}, "test_method_not_allowed": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_api_integration.TestErrorHandling.test_method_not_allowed", "name": "test_method_not_allowed", "type": null}}, "test_model_not_found_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mock_handle_chat"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "tests.test_api_integration.TestErrorHandling.test_model_not_found_error", "name": "test_model_not_found_error", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_api_integration.TestErrorHandling.test_model_not_found_error", "name": "test_model_not_found_error", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "test_model_not_found_error of TestErrorHandling", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_server_error_handling": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mock_handle_generate"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "tests.test_api_integration.TestErrorHandling.test_server_error_handling", "name": "test_server_error_handling", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_api_integration.TestErrorHandling.test_server_error_handling", "name": "test_server_error_handling", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "test_server_error_handling of TestErrorHandling", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_api_integration.TestErrorHandling.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_api_integration.TestErrorHandling", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestGenerateEndpoint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_api_integration.TestGenerateEndpoint", "name": "TestGenerateEndpoint", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_api_integration.TestGenerateEndpoint", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_api_integration", "mro": ["tests.test_api_integration.TestGenerateEndpoint", "builtins.object"], "names": {".class": "SymbolTable", "test_generate_endpoint_invalid_request": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_api_integration.TestGenerateEndpoint.test_generate_endpoint_invalid_request", "name": "test_generate_endpoint_invalid_request", "type": null}}, "test_generate_endpoint_success": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mock_handle_generate"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "tests.test_api_integration.TestGenerateEndpoint.test_generate_endpoint_success", "name": "test_generate_endpoint_success", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_api_integration.TestGenerateEndpoint.test_generate_endpoint_success", "name": "test_generate_endpoint_success", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "test_generate_endpoint_success of TestGenerateEndpoint", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_api_integration.TestGenerateEndpoint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_api_integration.TestGenerateEndpoint", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestHealthEndpoint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_api_integration.TestHealthEndpoint", "name": "TestHealthEndpoint", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_api_integration.TestHealthEndpoint", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_api_integration", "mro": ["tests.test_api_integration.TestHealthEndpoint", "builtins.object"], "names": {".class": "SymbolTable", "test_health_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_api_integration.TestHealthEndpoint.test_health_endpoint", "name": "test_health_endpoint", "type": null}}, "test_health_endpoint_structure": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_api_integration.TestHealthEndpoint.test_health_endpoint_structure", "name": "test_health_endpoint_structure", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_api_integration.TestHealthEndpoint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_api_integration.TestHealthEndpoint", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestMetricsEndpoint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_api_integration.TestMetricsEndpoint", "name": "TestMetricsEndpoint", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_api_integration.TestMetricsEndpoint", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_api_integration", "mro": ["tests.test_api_integration.TestMetricsEndpoint", "builtins.object"], "names": {".class": "SymbolTable", "test_metrics_endpoint_with_auth_no_credentials": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_api_integration.TestMetricsEndpoint.test_metrics_endpoint_with_auth_no_credentials", "name": "test_metrics_endpoint_with_auth_no_credentials", "type": null}}, "test_metrics_endpoint_with_non_admin_credentials": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_api_integration.TestMetricsEndpoint.test_metrics_endpoint_with_non_admin_credentials", "name": "test_metrics_endpoint_with_non_admin_credentials", "type": null}}, "test_metrics_endpoint_with_valid_admin_credentials": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_api_integration.TestMetricsEndpoint.test_metrics_endpoint_with_valid_admin_credentials", "name": "test_metrics_endpoint_with_valid_admin_credentials", "type": null}}, "test_metrics_endpoint_without_auth": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_api_integration.TestMetricsEndpoint.test_metrics_endpoint_without_auth", "name": "test_metrics_endpoint_without_auth", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_api_integration.TestMetricsEndpoint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_api_integration.TestMetricsEndpoint", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestProxyStatusEndpoint": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_api_integration.TestProxyStatusEndpoint", "name": "TestProxyStatusEndpoint", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_api_integration.TestProxyStatusEndpoint", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_api_integration", "mro": ["tests.test_api_integration.TestProxyStatusEndpoint", "builtins.object"], "names": {".class": "SymbolTable", "test_proxy_status_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mock_get_status"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "tests.test_api_integration.TestProxyStatusEndpoint.test_proxy_status_endpoint", "name": "test_proxy_status_endpoint", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_api_integration.TestProxyStatusEndpoint.test_proxy_status_endpoint", "name": "test_proxy_status_endpoint", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "test_proxy_status_endpoint of TestProxyStatusEndpoint", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_api_integration.TestProxyStatusEndpoint.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_api_integration.TestProxyStatusEndpoint", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestStreamingEndpoints": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_api_integration.TestStreamingEndpoints", "name": "TestStreamingEndpoints", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_api_integration.TestStreamingEndpoints", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_api_integration", "mro": ["tests.test_api_integration.TestStreamingEndpoints", "builtins.object"], "names": {".class": "SymbolTable", "test_chat_streaming_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mock_handle_stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "tests.test_api_integration.TestStreamingEndpoints.test_chat_streaming_endpoint", "name": "test_chat_streaming_endpoint", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_api_integration.TestStreamingEndpoints.test_chat_streaming_endpoint", "name": "test_chat_streaming_endpoint", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "test_chat_streaming_endpoint of TestStreamingEndpoints", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "test_generate_streaming_endpoint": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "mock_handle_stream"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "tests.test_api_integration.TestStreamingEndpoints.test_generate_streaming_endpoint", "name": "test_generate_streaming_endpoint", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_api_integration.TestStreamingEndpoints.test_generate_streaming_endpoint", "name": "test_generate_streaming_endpoint", "type": {".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": "test_generate_streaming_endpoint of TestStreamingEndpoints", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_of_any": 7}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_api_integration.TestStreamingEndpoints.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_api_integration.TestStreamingEndpoints", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_api_integration.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_api_integration.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_api_integration.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_api_integration.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_api_integration.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_api_integration.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "app": {".class": "SymbolTableNode", "cross_ref": "llm_proxy_server.main.app", "kind": "Gdef"}, "get_current_user": {".class": "SymbolTableNode", "cross_ref": "llm_proxy_server.main.get_current_user", "kind": "Gdef"}, "get_settings": {".class": "SymbolTableNode", "cross_ref": "llm_proxy_server.config.get_settings", "kind": "Gdef"}, "patch": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.patch", "kind": "Gdef"}, "pytest": {".class": "SymbolTableNode", "cross_ref": "pytest", "kind": "Gdef"}}, "path": "C:\\home-repos\\llm_proxy_server\\llm-proxy-server\\tests\\test_api_integration.py"}