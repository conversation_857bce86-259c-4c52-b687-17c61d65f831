{"data_mtime": 1753376944, "dep_lines": [4, 7, 3, 5, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["unittest.mock", "llm_proxy_server.load_balancer", "pytest", "asyncio", "builtins", "_frozen_importlib", "_pytest", "_pytest._code", "_pytest._code.code", "_pytest.mark", "_pytest.mark.structures", "_pytest.raises", "abc", "llm_proxy_server", "re", "typing"], "hash": "ad5405b7657e40e96362a6fad3296d7bd11468b0", "id": "tests.test_load_balancer_new", "ignore_all": false, "interface_hash": "43566f69ab45901efc2fafbf9462b043a1b9d80f", "mtime": 1753376943, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\llm_proxy_server\\llm-proxy-server\\tests\\test_load_balancer_new.py", "plugin_data": null, "size": 9524, "suppressed": [], "version_id": "1.15.0"}