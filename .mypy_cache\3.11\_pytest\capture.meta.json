{"data_mtime": 1753376691, "dep_lines": [36, 8, 34, 37, 38, 40, 43, 4, 6, 7, 11, 12, 14, 15, 16, 17, 18, 32, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 10, 10, 5, 5, 5, 25, 5, 30, 30, 30, 30, 30], "dependencies": ["_pytest.config.argparsing", "collections.abc", "_pytest.config", "_pytest.deprecated", "_pytest.fixtures", "_pytest.nodes", "_pytest.reports", "__future__", "abc", "collections", "contextlib", "io", "os", "sys", "tempfile", "types", "typing", "typing_extensions", "builtins", "_frozen_importlib", "_io", "_typeshed", "pluggy", "pluggy._hooks"], "hash": "e121aa7e7e74178e2db9dc26fac6d53e93fd2514", "id": "_pytest.capture", "ignore_all": true, "interface_hash": "2b791b808ded92d4391ce362be6a03635a7cb053", "mtime": 1753376605, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\llm_proxy_server\\venv\\Lib\\site-packages\\_pytest\\capture.py", "plugin_data": null, "size": 36829, "suppressed": [], "version_id": "1.15.0"}