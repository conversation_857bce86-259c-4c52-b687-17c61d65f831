{"data_mtime": 1753376688, "dep_lines": [27, 28, 4, 5, 20, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 10, 5, 30, 30, 30, 30, 30], "dependencies": ["iniconfig.exceptions", "iniconfig._parse", "__future__", "typing", "os", "builtins", "_frozen_importlib", "_io", "abc", "io", "types"], "hash": "c6c3473689bc62afd73a81fc6470dd2a45fcd281", "id": "iniconfig", "ignore_all": true, "interface_hash": "c768897b3cf6806ef4069079b5c1dc1751f005fe", "mtime": 1753376604, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\llm_proxy_server\\venv\\Lib\\site-packages\\iniconfig\\__init__.py", "plugin_data": null, "size": 5462, "suppressed": [], "version_id": "1.15.0"}