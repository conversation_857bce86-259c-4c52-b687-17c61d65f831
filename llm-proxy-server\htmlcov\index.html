<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_dca529e9.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">81%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.0">coverage.py v7.10.0</a>,
            created at 2025-07-24 13:32 -0400
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1___init___py.html">llm_proxy_server\__init__.py</a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_auth_py.html">llm_proxy_server\auth.py</a></td>
                <td>61</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="59 61">97%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_config_py.html">llm_proxy_server\config.py</a></td>
                <td>25</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="25 25">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_load_balancer_py.html">llm_proxy_server\load_balancer.py</a></td>
                <td>87</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="83 87">95%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_main_py.html">llm_proxy_server\main.py</a></td>
                <td>140</td>
                <td>23</td>
                <td>0</td>
                <td class="right" data-ratio="117 140">84%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_models_py.html">llm_proxy_server\models.py</a></td>
                <td>91</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="91 91">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_monitoring_py.html">llm_proxy_server\monitoring.py</a></td>
                <td>108</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="104 108">96%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_bdae19fbfd4018c1_proxy_manager_py.html">llm_proxy_server\proxy_manager.py</a></td>
                <td>257</td>
                <td>116</td>
                <td>0</td>
                <td class="right" data-ratio="141 257">55%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>771</td>
                <td>149</td>
                <td>0</td>
                <td class="right" data-ratio="622 771">81%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.10.0">coverage.py v7.10.0</a>,
            created at 2025-07-24 13:32 -0400
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_bdae19fbfd4018c1_proxy_manager_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_bdae19fbfd4018c1___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
