{"data_mtime": 1753376691, "dep_lines": [26, 8, 18, 20, 21, 22, 23, 27, 28, 30, 117, 6, 10, 11, 12, 13, 14, 15, 16, 21, 115, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 20, 5, 10, 10, 10, 10, 5, 10, 5, 20, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest.config.argparsing", "collections.abc", "_pytest.pathlib", "_pytest.reports", "_pytest.nodes", "_pytest._io", "_pytest.config", "_pytest.deprecated", "_pytest.fixtures", "_pytest.main", "_pytest.warning_types", "__future__", "dataclasses", "errno", "json", "os", "pathlib", "tempfile", "typing", "_pytest", "warnings", "builtins", "_frozen_importlib", "_typeshed", "abc", "<PERSON><PERSON><PERSON><PERSON>", "enum", "pluggy", "pluggy._hooks", "pluggy._manager", "types"], "hash": "1f78e9924d375ff166bf751701890bb886451a26", "id": "_pytest.cacheprovider", "ignore_all": true, "interface_hash": "65cb02e86a43b2c61622fb28f94c4ea36a3ccb86", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\llm_proxy_server\\venv\\Lib\\site-packages\\_pytest\\cacheprovider.py", "plugin_data": null, "size": 22375, "suppressed": [], "version_id": "1.15.0"}