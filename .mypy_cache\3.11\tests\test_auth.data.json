{".class": "MypyFile", "_fullname": "tests.test_auth", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "AuthManager": {".class": "SymbolTableNode", "cross_ref": "llm_proxy_server.auth.AuthManager", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "TestAuthManager": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_auth.TestAuthManager", "name": "TestAuthManager", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_auth.TestAuthManager", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_auth", "mro": ["tests.test_auth.TestAuthManager", "builtins.object"], "names": {".class": "SymbolTable", "test_authenticate_exception_handling": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "auth_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_auth.TestAuthManager.test_authenticate_exception_handling", "name": "test_authenticate_exception_handling", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_auth.TestAuthManager.test_authenticate_exception_handling", "name": "test_authenticate_exception_handling", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_authenticate_invalid_password": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "auth_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_auth.TestAuthManager.test_authenticate_invalid_password", "name": "test_authenticate_invalid_password", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_auth.TestAuthManager.test_authenticate_invalid_password", "name": "test_authenticate_invalid_password", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_authenticate_invalid_token_format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "auth_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_auth.TestAuthManager.test_authenticate_invalid_token_format", "name": "test_authenticate_invalid_token_format", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_auth.TestAuthManager.test_authenticate_invalid_token_format", "name": "test_authenticate_invalid_token_format", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_authenticate_nonexistent_user": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "auth_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_auth.TestAuthManager.test_authenticate_nonexistent_user", "name": "test_authenticate_nonexistent_user", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_auth.TestAuthManager.test_authenticate_nonexistent_user", "name": "test_authenticate_nonexistent_user", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_authenticate_valid_regular_user": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "auth_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_auth.TestAuthManager.test_authenticate_valid_regular_user", "name": "test_authenticate_valid_regular_user", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_auth.TestAuthManager.test_authenticate_valid_regular_user", "name": "test_authenticate_valid_regular_user", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_authenticate_valid_user": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "auth_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_auth.TestAuthManager.test_authenticate_valid_user", "name": "test_authenticate_valid_user", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_auth.TestAuthManager.test_authenticate_valid_user", "name": "test_authenticate_valid_user", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_authenticate_with_colon_in_password": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "temp_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_auth.TestAuthManager.test_authenticate_with_colon_in_password", "name": "test_authenticate_with_colon_in_password", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_auth.TestAuthManager.test_authenticate_with_colon_in_password", "name": "test_authenticate_with_colon_in_password", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_get_user_count": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "auth_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_auth.TestAuthManager.test_get_user_count", "name": "test_get_user_count", "type": null}}, "test_init_with_existing_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "users_config_file"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_auth.TestAuthManager.test_init_with_existing_file", "name": "test_init_with_existing_file", "type": null}}, "test_init_with_missing_file": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "temp_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_auth.TestAuthManager.test_init_with_missing_file", "name": "test_init_with_missing_file", "type": null}}, "test_is_user_admin": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "auth_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_auth.TestAuthManager.test_is_user_admin", "name": "test_is_user_admin", "type": null}}, "test_load_users_file_read_error": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "temp_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_auth.TestAuthManager.test_load_users_file_read_error", "name": "test_load_users_file_read_error", "type": null}}, "test_load_users_minimal_format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "temp_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_auth.TestAuthManager.test_load_users_minimal_format", "name": "test_load_users_minimal_format", "type": null}}, "test_load_users_with_comments_and_empty_lines": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "temp_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_auth.TestAuthManager.test_load_users_with_comments_and_empty_lines", "name": "test_load_users_with_comments_and_empty_lines", "type": null}}, "test_load_users_with_invalid_format": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "temp_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_auth.TestAuthManager.test_load_users_with_invalid_format", "name": "test_load_users_with_invalid_format", "type": null}}, "test_reload_users": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "users_config_file"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_auth.TestAuthManager.test_reload_users", "name": "test_reload_users", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_auth.TestAuthManager.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_auth.TestAuthManager", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "TestAuthManagerEdgeCases": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "tests.test_auth.TestAuthManagerEdgeCases", "name": "TestAuthManagerEdgeCases", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "tests.test_auth.TestAuthManagerEdgeCases", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "tests.test_auth", "mro": ["tests.test_auth.TestAuthManagerEdgeCases", "builtins.object"], "names": {".class": "SymbolTable", "test_admin_privilege_escalation_prevention": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "temp_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_auth.TestAuthManagerEdgeCases.test_admin_privilege_escalation_prevention", "name": "test_admin_privilege_escalation_prevention", "type": null}}, "test_authenticate_empty_credentials": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "temp_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_auth.TestAuthManagerEdgeCases.test_authenticate_empty_credentials", "name": "test_authenticate_empty_credentials", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_auth.TestAuthManagerEdgeCases.test_authenticate_empty_credentials", "name": "test_authenticate_empty_credentials", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_authenticate_sql_injection_attempt": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "auth_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_auth.TestAuthManagerEdgeCases.test_authenticate_sql_injection_attempt", "name": "test_authenticate_sql_injection_attempt", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_auth.TestAuthManagerEdgeCases.test_authenticate_sql_injection_attempt", "name": "test_authenticate_sql_injection_attempt", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_authenticate_timing_attack_resistance": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "auth_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_auth.TestAuthManagerEdgeCases.test_authenticate_timing_attack_resistance", "name": "test_authenticate_timing_attack_resistance", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_auth.TestAuthManagerEdgeCases.test_authenticate_timing_attack_resistance", "name": "test_authenticate_timing_attack_resistance", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_authenticate_unicode_handling": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "temp_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_auth.TestAuthManagerEdgeCases.test_authenticate_unicode_handling", "name": "test_authenticate_unicode_handling", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_auth.TestAuthManagerEdgeCases.test_authenticate_unicode_handling", "name": "test_authenticate_unicode_handling", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_authenticate_very_long_credentials": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "temp_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine", "is_decorated"], "fullname": "tests.test_auth.TestAuthManagerEdgeCases.test_authenticate_very_long_credentials", "name": "test_authenticate_very_long_credentials", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "tests.test_auth.TestAuthManagerEdgeCases.test_authenticate_very_long_credentials", "name": "test_authenticate_very_long_credentials", "type": {".class": "AnyType", "missing_import_name": null, "source_any": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, "type_of_any": 7}}}}, "test_concurrent_user_loading": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "temp_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_auth.TestAuthManagerEdgeCases.test_concurrent_user_loading", "name": "test_concurrent_user_loading", "type": null}}, "test_user_file_modification_detection": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "temp_dir"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "tests.test_auth.TestAuthManagerEdgeCases.test_user_file_modification_detection", "name": "test_user_file_modification_detection", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "tests.test_auth.TestAuthManagerEdgeCases.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "tests.test_auth.TestAuthManagerEdgeCases", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_auth.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_auth.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_auth.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_auth.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_auth.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "tests.test_auth.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "mock_open": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.mock_open", "kind": "Gdef"}, "patch": {".class": "SymbolTableNode", "cross_ref": "unittest.mock.patch", "kind": "Gdef"}, "pytest": {".class": "SymbolTableNode", "cross_ref": "pytest", "kind": "Gdef"}}, "path": "C:\\home-repos\\llm_proxy_server\\llm-proxy-server\\tests\\test_auth.py"}