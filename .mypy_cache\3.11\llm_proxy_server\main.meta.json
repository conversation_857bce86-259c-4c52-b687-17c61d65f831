{"data_mtime": 1753383555, "dep_lines": [12, 13, 14, 17, 18, 19, 20, 21, 22, 3, 4, 5, 6, 7, 8, 10, 11, 16, 344, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 5, 5, 5, 5, 5, 10, 10, 10, 5, 5, 5, 10, 5, 5, 10, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["fastapi.middleware.cors", "fastapi.responses", "fastapi.security", "llm_proxy_server.auth", "llm_proxy_server.config", "llm_proxy_server.load_balancer", "llm_proxy_server.models", "llm_proxy_server.monitoring", "llm_proxy_server.proxy_manager", "asyncio", "json", "time", "contextlib", "datetime", "typing", "structlog", "<PERSON><PERSON><PERSON>", "llm_proxy_server", "u<PERSON><PERSON>", "builtins", "_asyncio", "_contextvars", "_frozen_importlib", "_typeshed", "abc", "asyncio.exceptions", "asyncio.protocols", "configparser", "enum", "fastapi.applications", "fastapi.exceptions", "fastapi.param_functions", "fastapi.params", "fastapi.routing", "fastapi.security.base", "fastapi.security.http", "json.encoder", "logging", "os", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "starlette", "starlette.applications", "starlette.background", "starlette.datastructures", "starlette.exceptions", "starlette.middleware", "starlette.middleware.cors", "starlette.requests", "starlette.responses", "starlette.routing", "structlog._base", "structlog.processors", "structlog.stdlib", "structlog.typing", "typing_extensions", "uvicorn._types"], "hash": "6b5863863a519af2f1a1ff536ca9306494b03ef6", "id": "llm_proxy_server.main", "ignore_all": false, "interface_hash": "77158c24136fc2513f07433a1caa6c8efcb8dfbb", "mtime": **********, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\llm_proxy_server\\llm-proxy-server\\llm_proxy_server\\main.py", "plugin_data": null, "size": 11205, "suppressed": [], "version_id": "1.15.0"}