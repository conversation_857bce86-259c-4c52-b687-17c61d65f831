{"data_mtime": 1753376691, "dep_lines": [12, 13, 14, 18, 6, 16, 19, 23, 4, 7, 8, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 10, 10, 5, 5, 5, 5, 25, 5, 10, 5, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["_pytest.assertion.rewrite", "_pytest.assertion.truncate", "_pytest.assertion.util", "_pytest.config.argparsing", "collections.abc", "_pytest.config", "_pytest.nodes", "_pytest.main", "__future__", "sys", "typing", "builtins", "_frozen_importlib", "abc", "importlib", "importlib._abc", "importlib.abc", "pluggy", "pluggy._hooks", "pluggy._tracing", "types"], "hash": "13efa0910df75e7a3315974bd4893cd374f17325", "id": "_pytest.assertion", "ignore_all": true, "interface_hash": "99ba1e6e659b9cdfbe09b3abae26567e451a58f5", "mtime": 1753376605, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\llm_proxy_server\\venv\\Lib\\site-packages\\_pytest\\assertion\\__init__.py", "plugin_data": null, "size": 7120, "suppressed": [], "version_id": "1.15.0"}