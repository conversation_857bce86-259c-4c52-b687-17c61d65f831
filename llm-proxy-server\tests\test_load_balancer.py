"""Tests for load balancer functionality"""

import pytest
from unittest.mock import patch
import asyncio

from llm_proxy_server.load_balancer import <PERSON>adBalancer


@pytest.mark.unit
@pytest.mark.load_balancer
class TestLoadBalancer:
    """Test LoadBalancer class"""
    
    def test_init_round_robin(self):
        """Test LoadBalancer initialization with round robin strategy"""
        lb = LoadBalancer("round_robin")
        assert lb.strategy == "round_robin"
        assert len(lb.host_connections) == 0
        assert len(lb.host_weights) == 0
        assert len(lb.host_health) == 0
    
    def test_init_least_connections(self):
        """Test LoadBalancer initialization with least connections strategy"""
        lb = LoadBalancer("least_connections")
        assert lb.strategy == "least_connections"
        assert len(lb.host_connections) == 0
    
    def test_init_weighted_random(self):
        """Test LoadBalancer initialization with weighted random strategy"""
        lb = LoadBalancer("weighted_random")
        assert lb.strategy == "weighted_random"
        assert len(lb.host_connections) == 0
    
    def test_init_invalid_strategy(self):
        """Test LoadBalancer initialization with invalid strategy"""
        lb = LoadBalancer("invalid_strategy")
        assert lb.strategy == "invalid_strategy"
    
    @pytest.mark.asyncio
    async def test_select_host_no_hosts(self):
        """Test selecting host when no hosts available"""
        lb = LoadBalancer("round_robin")
        
        with pytest.raises(ValueError, match="No available hosts"):
            await lb.select_host("test-model", [])
    
    @pytest.mark.asyncio
    async def test_select_host_single_host(self):
        """Test selecting host when only one host available"""
        lb = LoadBalancer("round_robin")
        hosts = ["http://localhost:11434"]
        
        result = await lb.select_host("test-model", hosts)
        assert result == "http://localhost:11434"
    
    @pytest.mark.asyncio
    async def test_round_robin_selection(self):
        """Test round robin host selection"""
        lb = LoadBalancer("round_robin")
        hosts = [
            "http://localhost:11434",
            "http://localhost:11435",
            "http://localhost:11436"
        ]
        
        # Test multiple selections to verify round-robin behavior
        selections = []
        for _ in range(6):  # Two full rounds
            host = await lb.select_host("test-model", hosts)
            selections.append(host)
        
        # Should cycle through hosts
        assert selections[0] == selections[3]  # First and fourth should be same
        assert selections[1] == selections[4]  # Second and fifth should be same
        assert selections[2] == selections[5]  # Third and sixth should be same
    
    @pytest.mark.asyncio
    async def test_least_connections_selection(self):
        """Test least connections host selection"""
        lb = LoadBalancer("least_connections")
        hosts = [
            "http://localhost:11434",
            "http://localhost:11435",
            "http://localhost:11436"
        ]
        
        # Initially all have 0 connections
        host1 = await lb.select_host("test-model", hosts)
        assert host1 in hosts
        
        # Simulate connection on first host
        lb.increment_connections(host1)
        
        # Next selection should prefer hosts with fewer connections
        host2 = await lb.select_host("test-model", hosts)
        assert host2 in hosts
        
        # If host2 is different from host1, it should have fewer connections
        if host2 != host1:
            assert lb.host_connections[host2] < lb.host_connections[host1]
    
    @pytest.mark.asyncio
    async def test_weighted_random_selection(self):
        """Test weighted random host selection"""
        lb = LoadBalancer("weighted_random")
        hosts = [
            "http://localhost:11434",
            "http://localhost:11435"
        ]
        
        # Set different weights
        lb.set_host_weight(hosts[0], 1.0)
        lb.set_host_weight(hosts[1], 10.0)  # Much higher weight
        
        # Test multiple selections
        selections = []
        for _ in range(100):
            host = await lb.select_host("test-model", hosts)
            selections.append(host)
        
        # Host with higher weight should be selected more often
        host1_count = selections.count(hosts[1])
        assert host1_count > 50  # Should be significantly more than half
    
    @pytest.mark.asyncio
    async def test_random_selection(self):
        """Test random host selection"""
        lb = LoadBalancer("random")
        hosts = [
            "http://localhost:11434",
            "http://localhost:11435",
            "http://localhost:11436"
        ]
        
        # Test that it returns valid hosts
        for _ in range(10):
            host = await lb.select_host("test-model", hosts)
            assert host in hosts
    
    @pytest.mark.asyncio
    async def test_unhealthy_hosts_filtered(self):
        """Test that unhealthy hosts are filtered out"""
        lb = LoadBalancer("round_robin")
        hosts = [
            "http://localhost:11434",
            "http://localhost:11435",
            "http://localhost:11436"
        ]
        
        # Mark one host as unhealthy
        lb.set_host_health(hosts[1], False)
        
        # Should only select from healthy hosts
        selections = []
        for _ in range(10):
            host = await lb.select_host("test-model", hosts)
            selections.append(host)
        
        # Should never select the unhealthy host
        assert hosts[1] not in selections
        # Should only select from healthy hosts
        for selection in selections:
            assert selection in [hosts[0], hosts[2]]
    
    @pytest.mark.asyncio
    async def test_all_hosts_unhealthy_fallback(self):
        """Test fallback when all hosts are unhealthy"""
        lb = LoadBalancer("round_robin")
        hosts = ["http://localhost:11434", "http://localhost:11435"]
        
        # Mark all hosts as unhealthy
        for host in hosts:
            lb.set_host_health(host, False)
        
        # Should still return a host (fallback behavior)
        host = await lb.select_host("test-model", hosts)
        assert host in hosts
    
    def test_increment_decrement_connections(self):
        """Test incrementing and decrementing connection counts"""
        lb = LoadBalancer("least_connections")
        host = "http://localhost:11434"
        
        assert lb.host_connections[host] == 0
        
        lb.increment_connections(host)
        assert lb.host_connections[host] == 1
        
        lb.increment_connections(host)
        assert lb.host_connections[host] == 2
        
        lb.decrement_connections(host)
        assert lb.host_connections[host] == 1
        
        lb.decrement_connections(host)
        assert lb.host_connections[host] == 0
    
    def test_decrement_connections_below_zero(self):
        """Test that connections don't go below zero"""
        lb = LoadBalancer("least_connections")
        host = "http://localhost:11434"
        
        # Should not go below 0
        lb.decrement_connections(host)
        assert lb.host_connections[host] == 0
    
    def test_set_host_weight(self):
        """Test setting host weight"""
        lb = LoadBalancer("weighted_random")
        host = "http://localhost:11434"
        
        lb.set_host_weight(host, 2.5)
        assert lb.host_weights[host] == 2.5
    
    def test_set_host_health(self):
        """Test setting host health status"""
        lb = LoadBalancer("round_robin")
        host = "http://localhost:11434"
        
        # Default should be healthy
        assert lb.host_health[host] is True
        
        lb.set_host_health(host, False)
        assert lb.host_health[host] is False
        
        lb.set_host_health(host, True)
        assert lb.host_health[host] is True
    
    def test_get_host_stats(self):
        """Test getting host statistics"""
        lb = LoadBalancer("least_connections")
        hosts = ["http://localhost:11434", "http://localhost:11435"]
        
        # Set up some data
        lb.increment_connections(hosts[0])
        lb.increment_connections(hosts[0])
        lb.increment_connections(hosts[1])
        lb.set_host_weight(hosts[0], 1.5)
        lb.set_host_weight(hosts[1], 2.0)
        lb.set_host_health(hosts[1], False)
        
        stats = lb.get_host_stats()
        
        assert hosts[0] in stats
        assert hosts[1] in stats
        assert stats[hosts[0]]["connections"] == 2
        assert stats[hosts[1]]["connections"] == 1
        assert stats[hosts[0]]["weight"] == 1.5
        assert stats[hosts[1]]["weight"] == 2.0
        assert stats[hosts[0]]["healthy"] is True
        assert stats[hosts[1]]["healthy"] is False
    
    def test_reset_stats(self):
        """Test resetting all statistics"""
        lb = LoadBalancer("least_connections")
        host = "http://localhost:11434"
        
        # Set up some data
        lb.increment_connections(host)
        lb.increment_connections(host)
        
        assert lb.host_connections[host] == 2
        
        lb.reset_stats()
        
        # Connections should be reset
        assert lb.host_connections[host] == 0
        # Round robin indices should be reset
        assert len(lb.round_robin_index) == 0
