{"data_mtime": 1753376691, "dep_lines": [9, 9, 10, 11, 7, 1, 1, 1, 1], "dep_prios": [10, 20, 5, 5, 5, 5, 30, 30, 30], "dependencies": ["_pytest.assertion.util", "_pytest.assertion", "_pytest.config", "_pytest.nodes", "__future__", "builtins", "_frozen_importlib", "abc", "typing"], "hash": "da422ba831b10d1dc7bc53c205910278b1e5253e", "id": "_pytest.assertion.truncate", "ignore_all": true, "interface_hash": "63b08c4a74e1a6490f4adfb881afb088cc4c4303", "mtime": 1753376605, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\llm_proxy_server\\venv\\Lib\\site-packages\\_pytest\\assertion\\truncate.py", "plugin_data": null, "size": 5437, "suppressed": [], "version_id": "1.15.0"}