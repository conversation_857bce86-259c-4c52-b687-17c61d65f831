{".class": "MypyFile", "_fullname": "_pytest.debugging", "future_import_flags": ["annotations"], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BaseReport": {".class": "SymbolTableNode", "cross_ref": "_pytest.reports.BaseReport", "kind": "Gdef"}, "CallInfo": {".class": "SymbolTableNode", "cross_ref": "_pytest.runner.CallInfo", "kind": "Gdef"}, "Callable": {".class": "SymbolTableNode", "cross_ref": "typing.Callable", "kind": "Gdef"}, "CaptureManager": {".class": "SymbolTableNode", "cross_ref": "_pytest.capture.CaptureManager", "kind": "Gdef"}, "Config": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.Config", "kind": "Gdef"}, "ConftestImportFailure": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.ConftestImportFailure", "kind": "Gdef"}, "ExceptionInfo": {".class": "SymbolTableNode", "cross_ref": "_pytest._code.code.ExceptionInfo", "kind": "Gdef"}, "Generator": {".class": "SymbolTableNode", "cross_ref": "typing.Generator", "kind": "Gdef"}, "Node": {".class": "SymbolTableNode", "cross_ref": "_pytest.nodes.Node", "kind": "Gdef"}, "Parser": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.argparsing.Parser", "kind": "Gdef"}, "PdbInvoke": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.debugging.PdbInvoke", "name": "PdbInvoke", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "_pytest.debugging.PdbInvoke", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.debugging", "mro": ["_pytest.debugging.PdbInvoke", "builtins.object"], "names": {".class": "SymbolTable", "pytest_exception_interact": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "call", "report"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.debugging.PdbInvoke.pytest_exception_interact", "name": "pytest_exception_interact", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0], "arg_names": ["self", "node", "call", "report"], "arg_types": ["_pytest.debugging.PdbInvoke", "_pytest.nodes.Node", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "_pytest.runner.CallInfo"}, "_pytest.reports.BaseReport"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_exception_interact of PdbInvoke", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_internalerror": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "excinfo"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.debugging.PdbInvoke.pytest_internalerror", "name": "pytest_internalerror", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "excinfo"], "arg_types": ["_pytest.debugging.PdbInvoke", {".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "_pytest._code.code.ExceptionInfo"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_internalerror of PdbInvoke", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.debugging.PdbInvoke.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.debugging.PdbInvoke", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PdbTrace": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.debugging.PdbTrace", "name": "PdbTrace", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "_pytest.debugging.PdbTrace", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.debugging", "mro": ["_pytest.debugging.PdbTrace", "builtins.object"], "names": {".class": "SymbolTable", "pytest_pyfunc_call": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "pyfuncitem"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_decorated"], "fullname": "_pytest.debugging.PdbTrace.pytest_pyfunc_call", "name": "pytest_pyfunc_call", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pyfuncitem"], "arg_types": ["_pytest.debugging.PdbTrace", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_pyfunc_call of PdbTrace", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, "builtins.object", "builtins.object"], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred"], "fullname": "_pytest.debugging.PdbTrace.pytest_pyfunc_call", "name": "pytest_pyfunc_call", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "pyfuncitem"], "arg_types": ["_pytest.debugging.PdbTrace", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_pyfunc_call of PdbTrace", "ret_type": {".class": "Instance", "args": [{".class": "NoneType"}, "builtins.object", "builtins.object"], "extra_attrs": null, "type_ref": "typing.Generator"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.debugging.PdbTrace.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.debugging.PdbTrace", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PytestPdbWrapper@152": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.debugging.PytestPdbWrapper@152", "name": "PytestPdbWrapper", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": ["fallback_to_any"], "fullname": "_pytest.debugging.PytestPdbWrapper@152", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.debugging", "mro": ["_pytest.debugging.PytestPdbWrapper@152", "builtins.object"], "names": {".class": "SymbolTable", "_continued": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "_pytest.debugging.PytestPdbWrapper@152._continued", "name": "_continued", "type": "builtins.bool"}}, "_pytest_capman": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "_pytest.debugging.PytestPdbWrapper@152._pytest_capman", "name": "_pytest_capman", "type": {".class": "UnionType", "items": ["_pytest.capture.CaptureManager", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "do_c": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "_pytest.debugging.PytestPdbWrapper@152.do_c", "name": "do_c", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "arg_types": ["_pytest.debugging.PytestPdbWrapper@152", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "do_cont": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "_pytest.debugging.PytestPdbWrapper@152.do_cont", "name": "do_cont", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "arg_types": ["_pytest.debugging.PytestPdbWrapper@152", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "do_continue": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.debugging.PytestPdbWrapper@152.do_continue", "name": "do_continue", "type": null}}, "do_debug": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.debugging.PytestPdbWrapper@152.do_debug", "name": "do_debug", "type": null}}, "do_exit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "_pytest.debugging.PytestPdbWrapper@152.do_exit", "name": "do_exit", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "arg_types": ["_pytest.debugging.PytestPdbWrapper@152", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "do_q": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_inferred", "has_explicit_value"], "fullname": "_pytest.debugging.PytestPdbWrapper@152.do_q", "name": "do_q", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "arg_types": ["_pytest.debugging.PytestPdbWrapper@152", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": null, "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "do_quit": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "arg"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.debugging.PytestPdbWrapper@152.do_quit", "name": "do_quit", "type": null}}, "get_stack": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "f", "t"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.debugging.PytestPdbWrapper@152.get_stack", "name": "get_stack", "type": null}}, "setup": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "f", "tb"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "<EMAIL>", "name": "setup", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "<EMAIL>", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.debugging.PytestPdbWrapper@152", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "PytestPluginManager": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.PytestPluginManager", "kind": "Gdef"}, "UsageError": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.exceptions.UsageError", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.debugging.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.debugging.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.debugging.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.debugging.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.debugging.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "_pytest.debugging.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "_enter_pdb": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["node", "excinfo", "rep"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.debugging._enter_pdb", "name": "_enter_pdb", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["node", "excinfo", "rep"], "arg_types": ["_pytest.nodes.Node", {".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "_pytest._code.code.ExceptionInfo"}, "_pytest.reports.BaseReport"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_enter_pdb", "ret_type": "_pytest.reports.BaseReport", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_postmortem_exc_or_tb": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["excinfo"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.debugging._postmortem_exc_or_tb", "name": "_postmortem_exc_or_tb", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["excinfo"], "arg_types": [{".class": "Instance", "args": ["builtins.BaseException"], "extra_attrs": null, "type_ref": "_pytest._code.code.ExceptionInfo"}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_postmortem_exc_or_tb", "ret_type": {".class": "UnionType", "items": ["types.TracebackType", "builtins.BaseException"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_validate_usepdb_cls": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["value"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.debugging._validate_usepdb_cls", "name": "_validate_usepdb_cls", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["value"], "arg_types": ["builtins.str"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_validate_usepdb_cls", "ret_type": {".class": "TupleType", "implicit": false, "items": ["builtins.str", "builtins.str"], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "annotations": {".class": "SymbolTableNode", "cross_ref": "__future__.annotations", "kind": "Gdef"}, "argparse": {".class": "SymbolTableNode", "cross_ref": "<PERSON><PERSON><PERSON><PERSON>", "kind": "Gdef"}, "functools": {".class": "SymbolTableNode", "cross_ref": "functools", "kind": "Gdef"}, "hookimpl": {".class": "SymbolTableNode", "cross_ref": "_pytest.config.hookimpl", "kind": "Gdef"}, "maybe_wrap_pytest_function_for_tracing": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["pyfuncitem"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.debugging.maybe_wrap_pytest_function_for_tracing", "name": "maybe_wrap_pytest_function_for_tracing", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["pyfuncitem"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "maybe_wrap_pytest_function_for_tracing", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "outcomes": {".class": "SymbolTableNode", "cross_ref": "_pytest.outcomes", "kind": "Gdef"}, "post_mortem": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tb_or_exc"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.debugging.post_mortem", "name": "post_mortem", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["tb_or_exc"], "arg_types": [{".class": "UnionType", "items": ["types.TracebackType", "builtins.BaseException"], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "post_mortem", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytestPDB": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "_pytest.debugging.pytestPDB", "name": "pytestPDB", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "_pytest.debugging.pytestPDB", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "_pytest.debugging", "mro": ["_pytest.debugging.pytestPDB", "builtins.object"], "names": {".class": "SymbolTable", "_config": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "_pytest.debugging.pytestPDB._config", "name": "_config", "type": {".class": "UnionType", "items": ["_pytest.config.Config", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_get_pdb_wrapper_class": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["cls", "pdb_cls", "capman"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "_pytest.debugging.pytestPDB._get_pdb_wrapper_class", "name": "_get_pdb_wrapper_class", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "pdb_cls", "capman"], "arg_types": [{".class": "TypeType", "item": "_pytest.debugging.pytestPDB"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["_pytest.capture.CaptureManager", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_pdb_wrapper_class of pytestPDB", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "_pytest.debugging.pytestPDB._get_pdb_wrapper_class", "name": "_get_pdb_wrapper_class", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["cls", "pdb_cls", "capman"], "arg_types": [{".class": "TypeType", "item": "_pytest.debugging.pytestPDB"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "UnionType", "items": ["_pytest.capture.CaptureManager", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_pdb_wrapper_class of pytestPDB", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_import_pdb_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "capman"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "_pytest.debugging.pytestPDB._import_pdb_cls", "name": "_import_pdb_cls", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "capman"], "arg_types": [{".class": "TypeType", "item": "_pytest.debugging.pytestPDB"}, {".class": "UnionType", "items": ["_pytest.capture.CaptureManager", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_import_pdb_cls of pytestPDB", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "_pytest.debugging.pytestPDB._import_pdb_cls", "name": "_import_pdb_cls", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "capman"], "arg_types": [{".class": "TypeType", "item": "_pytest.debugging.pytestPDB"}, {".class": "UnionType", "items": ["_pytest.capture.CaptureManager", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_import_pdb_cls of pytestPDB", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_init_pdb": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "method", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "_pytest.debugging.pytestPDB._init_pdb", "name": "_init_pdb", "type": null}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "_pytest.debugging.pytestPDB._init_pdb", "name": "_init_pdb", "type": {".class": "CallableType", "arg_kinds": [0, 0, 2, 4], "arg_names": ["cls", "method", "args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "_pytest.debugging.pytestPDB"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": true, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_init_pdb of pytestPDB", "ret_type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_is_capturing": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["cls", "capman"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "_pytest.debugging.pytestPDB._is_capturing", "name": "_is_capturing", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "capman"], "arg_types": [{".class": "TypeType", "item": "_pytest.debugging.pytestPDB"}, {".class": "UnionType", "items": ["_pytest.capture.CaptureManager", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_capturing of pytestPDB", "ret_type": {".class": "UnionType", "items": ["builtins.str", "builtins.bool"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "_pytest.debugging.pytestPDB._is_capturing", "name": "_is_capturing", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["cls", "capman"], "arg_types": [{".class": "TypeType", "item": "_pytest.debugging.pytestPDB"}, {".class": "UnionType", "items": ["_pytest.capture.CaptureManager", {".class": "NoneType"}], "uses_pep604_syntax": true}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_is_capturing of pytestPDB", "ret_type": {".class": "UnionType", "items": ["builtins.str", "builtins.bool"], "uses_pep604_syntax": true}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "_pluginmanager": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "_pytest.debugging.pytestPDB._pluginmanager", "name": "_pluginmanager", "type": {".class": "UnionType", "items": ["_pytest.config.PytestPluginManager", {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "_recursive_debug": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "is_inferred", "has_explicit_value"], "fullname": "_pytest.debugging.pytestPDB._recursive_debug", "name": "_recursive_debug", "type": "builtins.int"}}, "_saved": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "_pytest.debugging.pytestPDB._saved", "name": "_saved", "type": {".class": "Instance", "args": [{".class": "TupleType", "implicit": false, "items": [{".class": "CallableType", "arg_kinds": [2, 4], "arg_names": [null, null], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": true, "name": null, "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}, {".class": "UnionType", "items": ["_pytest.config.PytestPluginManager", {".class": "NoneType"}], "uses_pep604_syntax": true}, {".class": "UnionType", "items": ["_pytest.config.Config", {".class": "NoneType"}], "uses_pep604_syntax": true}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}], "extra_attrs": null, "type_ref": "builtins.list"}}}, "_wrapped_pdb_cls": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready", "has_explicit_value"], "fullname": "_pytest.debugging.pytestPDB._wrapped_pdb_cls", "name": "_wrapped_pdb_cls", "type": {".class": "UnionType", "items": [{".class": "TupleType", "implicit": false, "items": [{".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}, {".class": "TypeType", "item": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}, {".class": "NoneType"}], "uses_pep604_syntax": true}}}, "set_trace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 2, 4], "arg_names": ["cls", "args", "kwargs"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_class", "is_decorated"], "fullname": "_pytest.debugging.pytestPDB.set_trace", "name": "set_trace", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["cls", "args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "_pytest.debugging.pytestPDB"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_trace of pytestPDB", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_initialized_in_class", "is_classmethod", "is_ready", "is_inferred"], "fullname": "_pytest.debugging.pytestPDB.set_trace", "name": "set_trace", "type": {".class": "CallableType", "arg_kinds": [0, 2, 4], "arg_names": ["cls", "args", "kwargs"], "arg_types": [{".class": "TypeType", "item": "_pytest.debugging.pytestPDB"}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": "cls"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "set_trace of pytestPDB", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "_pytest.debugging.pytestPDB.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "_pytest.debugging.pytestPDB", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "pytest_addoption": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["parser"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.debugging.pytest_addoption", "name": "pytest_addoption", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["parser"], "arg_types": ["_pytest.config.argparsing.Parser"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_addoption", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "pytest_configure": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["config"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.debugging.pytest_configure", "name": "pytest_configure", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["config"], "arg_types": ["_pytest.config.Config"], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "pytest_configure", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}, "types": {".class": "SymbolTableNode", "cross_ref": "types", "kind": "Gdef"}, "unittest": {".class": "SymbolTableNode", "cross_ref": "unittest", "kind": "Gdef"}, "wrap_pytest_function_for_tracing": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["pyfuncitem"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "_pytest.debugging.wrap_pytest_function_for_tracing", "name": "wrap_pytest_function_for_tracing", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["pyfuncitem"], "arg_types": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}], "bound_args": [], "def_extras": {"first_arg": null}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "wrap_pytest_function_for_tracing", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "path": "c:\\home-repos\\llm_proxy_server\\venv\\Lib\\site-packages\\_pytest\\debugging.py"}