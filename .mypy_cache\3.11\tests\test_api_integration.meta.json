{"data_mtime": 1753377919, "dep_lines": [4, 5, 7, 8, 121, 3, 470, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 5, 20, 10, 20, 5, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30, 30], "dependencies": ["unittest.mock", "fastapi.testclient", "llm_proxy_server.main", "llm_proxy_server.config", "llm_proxy_server.models", "pytest", "<PERSON><PERSON><PERSON>", "builtins", "_frozen_importlib", "_pytest", "_pytest.mark", "_pytest.mark.structures", "_typeshed", "abc", "fastapi.applications", "fastapi.exceptions", "fastapi.security", "fastapi.security.http", "http", "http.cookiejar", "httpx", "httpx._client", "httpx._models", "llm_proxy_server", "pydantic", "pydantic._internal", "pydantic._internal._model_construction", "pydantic.main", "starlette", "starlette.applications", "starlette.exceptions", "starlette.requests", "starlette.testclient", "types", "typing", "typing_extensions", "unittest"], "hash": "a28c6d13e57973a2cb18af6fc0db7147c6286edc", "id": "tests.test_api_integration", "ignore_all": false, "interface_hash": "cbb77b077a170f42b219472bcd6164a812b50d8f", "mtime": 1753378335, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\llm_proxy_server\\llm-proxy-server\\tests\\test_api_integration.py", "plugin_data": null, "size": 19150, "suppressed": [], "version_id": "1.15.0"}