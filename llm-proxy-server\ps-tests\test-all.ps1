# PowerShell script to run all LLM Proxy tests

Write-Host "🧪 LLM Proxy Complete Test Suite" -ForegroundColor Green
Write-Host "=================================" -ForegroundColor Green

# Get the directory where this script is located
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path

# Function to run a test script
function Run-Test {
    param(
        [string]$TestName,
        [string]$ScriptPath,
        [string]$Description
    )
    
    Write-Host "`n" + ("=" * 60) -ForegroundColor Cyan
    Write-Host "🧪 $TestName" -ForegroundColor Cyan
    Write-Host "📝 $Description" -ForegroundColor Gray
    Write-Host ("=" * 60) -ForegroundColor Cyan
    
    if (Test-Path $ScriptPath) {
        try {
            & $ScriptPath
            Write-Host "`n✅ $TestName completed successfully" -ForegroundColor Green
        }
        catch {
            Write-Host "`n❌ $TestName failed: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "❌ Test script not found: $ScriptPath" -ForegroundColor Red
    }
    
    Write-Host "`nPress Enter to continue to next test..." -ForegroundColor Yellow
    Read-Host
}

# Main execution
try {
    Write-Host "`n🎯 This will run all LLM Proxy tests in sequence:" -ForegroundColor Yellow
    Write-Host "1. Health Check - Verify proxy is running and healthy" -ForegroundColor White
    Write-Host "2. Load Balancing - Test different routing strategies" -ForegroundColor White
    Write-Host "3. Model Management - Check model distribution and management" -ForegroundColor White
    
    $confirm = Read-Host "`nRun complete test suite? (y/N)"
    if ($confirm -ne "y" -and $confirm -ne "Y") {
        Write-Host "Test suite cancelled." -ForegroundColor Gray
        exit 0
    }
    
    # Test 1: Health Check
    Run-Test -TestName "HEALTH CHECK" -ScriptPath "$scriptDir\test-proxy-health.ps1" -Description "Basic proxy health and functionality verification"
    
    # Test 2: Load Balancing
    Run-Test -TestName "LOAD BALANCING" -ScriptPath "$scriptDir\test-load-balancing.ps1" -Description "Load balancing strategy testing and optimization"
    
    # Test 3: Model Management
    Run-Test -TestName "MODEL MANAGEMENT" -ScriptPath "$scriptDir\test-model-management.ps1" -Description "Model distribution analysis and management operations"
    
    # Final Summary
    Write-Host "`n" + ("=" * 60) -ForegroundColor Green
    Write-Host "🎉 COMPLETE TEST SUITE FINISHED" -ForegroundColor Green
    Write-Host ("=" * 60) -ForegroundColor Green
    
    Write-Host "`n📊 Test Summary:" -ForegroundColor Cyan
    Write-Host "✅ Health Check - Proxy functionality verified" -ForegroundColor Green
    Write-Host "✅ Load Balancing - Optimal strategy configured" -ForegroundColor Green
    Write-Host "✅ Model Management - Model distribution analyzed" -ForegroundColor Green
    
    Write-Host "`n🎯 Your LLM Proxy is ready for production use!" -ForegroundColor Green
    Write-Host "🌐 Open WebUI: http://localhost:3000" -ForegroundColor Cyan
    Write-Host "📊 Proxy Status: http://localhost:11440/proxy/status" -ForegroundColor Cyan
    Write-Host "📈 Metrics: http://localhost:11440/proxy/metrics" -ForegroundColor Cyan
    
    Write-Host "`n💡 Quick Commands:" -ForegroundColor Yellow
    Write-Host "• Health check only: .\ps-tests\test-proxy-health.ps1" -ForegroundColor Gray
    Write-Host "• Load balancing only: .\ps-tests\test-load-balancing.ps1" -ForegroundColor Gray
    Write-Host "• Model management only: .\ps-tests\test-model-management.ps1" -ForegroundColor Gray
    
}
catch {
    Write-Host "`n❌ Test suite error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "Make sure the LLM Proxy is running: docker-compose -f docker-compose.local.yml up -d" -ForegroundColor Gray
}
