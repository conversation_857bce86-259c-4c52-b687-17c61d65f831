{"data_mtime": 1753376688, "dep_lines": [6, 2, 4, 5, 8, 9, 10, 11, 12, 13, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 10, 5, 10, 10, 10, 10, 5, 10, 5, 30, 30, 30, 30], "dependencies": ["collections.abc", "__future__", "ast", "bisect", "inspect", "textwrap", "tokenize", "types", "typing", "warnings", "builtins", "_frozen_importlib", "_typeshed", "abc", "typing_extensions"], "hash": "581ee059c8b767b6e43abbdc8349862375085007", "id": "_pytest._code.source", "ignore_all": true, "interface_hash": "830fd8e0de3294ae7ff88abe2514abf61acbd617", "mtime": 1753376605, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "c:\\home-repos\\llm_proxy_server\\venv\\Lib\\site-packages\\_pytest\\_code\\source.py", "plugin_data": null, "size": 7773, "suppressed": [], "version_id": "1.15.0"}